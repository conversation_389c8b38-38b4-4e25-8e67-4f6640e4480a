<?php

namespace App\Services\HLRV2;
use App\Services\HLRV2\{DatabaseService, HlrLookupService, MittoService, HlrServiceManager};
 class HlrService {
    protected $manager;
    protected $databaseService;
    protected $hlrLookupService;
    protected $mittoService;
    public function __construct(){
        $this->databaseService = new DatabaseService();
        $this->hlrLookupService = new HlrLookupService();
        $this->mittoService = new MittoService();
        
        $this->manager = new HlrServiceManager(
            $this->databaseService,
            $this->hlrLookupService,
            $this->mittoService
        );
    }

    public function lookup(array $numbers,int $is_hlr){
        try{
            $results = $this->manager->lookup($numbers);
            return $this->filterResult($results,$is_hlr);
        }catch(\Exception $e2){
	dd($e2); 
           //TODO: send sms faild look up
        }
        
    }

    private function filterResult($results,$check){
        if (!$check) {
            return [
                'accept' => array_map([$this, 'formatResult'], $results),
                'reject' => []
            ];
        }

        $partition = array_reduce($results, function($carry, $result) {
            $key =  ($result['live_status'] === 'LIVE' || $result['live_status'] === 'NOT_AVAILABLE_NETWORK_ONLY') ? 'accept' : 'reject';
            $carry[$key][] = $result;
            return $carry;
        }, ['accept' => [], 'reject' => []]);

        return array_map(function($items) {
            return array_map([$this, 'formatResult'], $items);
        }, $partition);
    }

    private function formatResult($result)
    {
        return [
            'number' => $result['number'],
            'networkName' => $result['map_network']
        ];
    }
 }
