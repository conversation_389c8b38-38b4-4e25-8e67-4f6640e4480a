<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\HLRV2\HlrService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use InvalidArgumentException;

class SendSmsV2 extends Controller
{
    protected $hlr;

    public function __construct(HlrService $hlr)
    {
        $this->hlr = $hlr;
    }

    public function index(Request $request)
    {
        // Validate request
        $this->validateRequest($request);

        // Process numbers
        $numbers = $this->processNumbers($request->numbers);
         $totalNumber = count($numbers);
	$skipNumbers = [];
	$undeliveredIndices = [];
        if (isset($request->is_skip) && $request->is_skip && count($numbers) > 2) {
            $whiteNumbers = $this->getWhiteNumbers($numbers)->toArray();
	  
	$numbersToProcess = array_diff($numbers, $whiteNumbers);
	          
  [$skippedProcessed, $skipNumbers] = $this->skip($numbersToProcess);
	        
    $numbers = array_merge($whiteNumbers, $skippedProcessed);
	
    $totalSkipNumbers = count($skipNumbers);
	
            $undeliveredCount = ceil($totalSkipNumbers * 0.3); // 3% of total, rounded up
		
    if ($undeliveredCount > 0 && $totalSkipNumbers > 0) {
                $undeliveredIndices = array_rand(array_flip(range(0, $totalSkipNumbers - 1)), $undeliveredCount);
                if (!is_array($undeliveredIndices)) {
                    $undeliveredIndices = [$undeliveredIndices]; // Convert to array if only one index
                }
            }
		
        }
  	
	$numbersData = array_map(function($number) use ($request) {
            return [
                'to' => $number,
                'message' => $request->message
            ];
        }, $numbers);
        // Process blocked and international numbers
        $blockedNumbers = $this->getBlockedNumbers($numbers);
        //dd($blockedNumbers);
        $numbers = array_diff($numbers, $blockedNumbers->toArray());
        $internationalNumbers = $this->getInternationalNumbers($numbers);
        $domesticNumbers = array_diff($numbers, $this->flattenNumberArray($internationalNumbers));

        // Check HLR
        $hlrResult = $this->hlr->lookup($domesticNumbers, $request->is_hlr ?? 0);
        $validNumbers = array_merge($internationalNumbers, $hlrResult['accept']);
//	if(isset($request->is_skip) && $request->is_skip){dd('s');}    
  //	if($request->message_id == 0){die('end');}
	  // Handle repeat prevention
        $repeatedNumbers = $this->getRepeatedNumbers(
            $validNumbers,
            $request->prevent_repeat ?? 0,
            $request->period ?? 0,
            $request->message_id,
            $request->sender,
            $request->message
        );
        $validNumbers = $this->removeRepeatedNumbers($validNumbers, $repeatedNumbers);

        // Create report
        $reportId = $this->createReport($request, $totalNumber, count($validNumbers), count($repeatedNumbers), 0);
        $channel_type = null;
	switch ($request->channel_type) {
    		case 1:
        		$channel_type = 'GOV';
       			 break;
    		default:
        		$channel_type = null;
	}
	$this->processBatchReportsUnified(
            $reportId,
            $request->message_id,
            $validNumbers,
            $blockedNumbers,
            $hlrResult['reject'],
            $repeatedNumbers,
            $skipNumbers,
            $numbersData,
	    $request->sender,
	    count($numbers)>100?10:9,
	    $channel_type,
	    $undeliveredIndices	

        );


        return "u-" . $reportId;
    }

    public function index_variable(Request $request)
    {
        try {
       // \Log::channel('api')->info(json_encode($request->numbers));
            $this->validateRequest($request);

            // Parse and validate JSON data
            $numbersData = $this->parseNumbersData($request->numbers);

            $numberToSend = array_column($numbersData, 'to');
            $totalNumber = count($numberToSend);

            // Process blocked numbers
            $blockedNumbers = $this->getBlockedNumbers($numberToSend);
            $numberToSend = array_diff($numberToSend, $blockedNumbers->toArray());
            $internationalNumbers = $this->getInternationalNumbers($numberToSend);

            $domesticNumbers = array_diff($numberToSend, $this->flattenNumberArray($internationalNumbers));
            // Process HLR checks

            $hlrResult = $this->hlr->lookup($domesticNumbers, 1);

            $validNumbers = array_merge($internationalNumbers, $hlrResult['accept']);
            // Process messages

            // Handle repeat prevention
            $numberRepeate = $this->getRepeatedNumbers(
                $validNumbers,
                $request->prevent_repeat ?? 0,
                $request->period ?? 0,
                $request->message_id,
                $request->sender,
                $request->message
            );
            // Filter out repeated numbers
            $validNumbers = $this->removeRepeatedNumbers($validNumbers, $numberRepeate);
            // Create report and process numbers
            $reportId = $this->createReport($request, $totalNumber, count($validNumbers), count($numberRepeate), 1);
	   $channel_type = null;
        switch ($request->channel_type) {
                case 1:
                        $channel_type = "GOV";
                         break;
                default:
                        $channel_type = null;
        }
            $this->processBatchReportsUnified(
                $reportId,
                $request->message_id,
                $validNumbers,
                $blockedNumbers,
                $hlrResult['reject'],
                $numberRepeate,
                [],
                $numbersData,
		$request->sender,
		10,
		$channel_type
            );
            return "u-" . $reportId;
        } catch (\Exception $e) {
            \Log::error('SMS Processing Error: ' . $e->getMessage());
            throw $e;
        }
    }
    protected function validateRequest(Request $request)
    {
        return $request->validate([
            'numbers' => 'required|string',
            'sender' => 'required|string',
            'message' => 'required|string',
            'message_id' => 'required|integer',
            'period' => 'integer',
            'is_skip' => 'boolean',
            'is_hlr' => 'boolean',
            'prevent_repeat' => 'integer'
        ]);
    }
    protected function processNumbers(string $numbers): array
    {
        $numbers = str_replace(';', ',', $numbers);
        $numbers = explode(',', $numbers);
        return array_unique($numbers);
    }

    protected function getBlockedNumbers(array $numbers): Collection
    {
        return DB::table('numbers')
            ->whereIn('number', $numbers)
            ->pluck('number');
    }

    protected function getWhiteNumbers(array $numbers): Collection
    {
        return DB::table('saco_number')
            ->whereIn('number', $numbers)
            ->pluck('number');
    }

    protected function getRepeatedNumbers(array $numbers, int $preventRepeat, int $period, int $messageId, string $sender, string $message): array
    {
	if($messageId == 0){
		$period = 2;
	}
        
	if (!$preventRepeat || !$period) {
            return [];
        }

        $cutoffDate = Carbon::now()->subMinutes($period);

        return DB::table('number_report2')
            ->join('reports2', 'reports2.id', '=', 'number_report2.reports_id')
            ->whereIn('number_report2.number', $this->flattenNumberArray($numbers))
            ->where('reports2.created_at', '>', $cutoffDate)
            ->where('reports2.sender', $sender)
            ->where('reports2.message', $message)
            ->pluck('number_report2.number')
            ->unique()
            ->toArray();
    }

    protected function createReport(Request $request, int $totalNumbers, int $filterNumbers, int $repeatCount, int $is_variables = 0): int
    {
        return DB::table('reports2')->insertGetId([
            'totalNumbers' => $totalNumbers,
            'filterNumbers' => $filterNumbers,
            'sender' => $request->sender,
            'point' => $this->calculate_points($this->detectSMPPCoding($request->message) == 8 ? "ar" : "en", $request->message),
            'priority'=> $totalNumbers >= 100 ?10:9,
	    'message' => $request->message,
            'repeatNumber' => $repeatCount,
            'message_id' => $request->message_id,
            'smpp_coding' => $is_variables == 1 ? 8 : $this->detectSMPPCoding($request->message),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'is_variables' => $is_variables
        ]);
    }

    protected function processBatchReportsUnified(
        int $reportId,
        int $portal_message_id,
        array $validNumbers,
        Collection $blockedNumbers,
        array $rejectedHlr,
        array $repeatedNumbers,
        array $skipNumbers,
        ?array $numbersData = null,
        string $sender = null,
	int $priority = 10,
	string $channle_type = null,
	array $undeliveredIndices = []
    ): void {
        $batch = [];
        $statusBatch = [];
        $batchSize = 1000;
        $messagesByNumber = [];
        if ($numbersData != null) {
            foreach ($numbersData as $data) {
                $messagesByNumber[$data['to']] = $data['message'];
            }
        }

        // Process valid numbers
        foreach ($validNumbers as $number) {
            $message = $messagesByNumber[$number['number']] ?? null;
            $batch[] = $this->createNumberReportEntry($reportId, $portal_message_id, $number, 0, $message,$sender,$priority,$channle_type);
            $this->addToStatusBatch($statusBatch, $portal_message_id, $number, 'ACCEPTED');
        }

        // Process blocked numbers
        foreach ($blockedNumbers as $number) {
            $numberData = ['number' => $number, 'networkName' => null];
            $message = $messagesByNumber[$number] ?? null;
            $batch[] = $this->createNumberReportEntry($reportId, $portal_message_id, $numberData, -1, $message,$sender);
            $this->addToStatusBatch($statusBatch, $portal_message_id, $numberData, 'BLOCKED');
        }
        // Process HLR rejected numbers
        foreach ($rejectedHlr as $number) {
            $message = $messagesByNumber[$number['number']] ?? null;
            $batch[] = $this->createNumberReportEntry($reportId, $portal_message_id, $number, -1, $message, $sender);
            $this->addToStatusBatch($statusBatch, $portal_message_id, $number, 'BLOCKED_HLR');
        }

        // Process repeated numbers
        foreach ($repeatedNumbers as $number) {
            $numberData = ['number' => $number, 'networkName' => null];
            $message = $messagesByNumber[$number] ?? null;
            $batch[] = $this->createNumberReportEntry($reportId, $portal_message_id, $numberData, -2, $message,$sender);
            $this->addToStatusBatch($statusBatch, $portal_message_id, $numberData, 'REPEAT');
        }

	// Process skipped numbers
        foreach ($skipNumbers as $index => $number) {
          
	$numberData = ['number' => $number, 'networkName' => null];
            $batch[] = $this->createNumberReportEntry($reportId, $portal_message_id, $numberData, -3, $message,$sender);
            // Randomly assign UNDELIVERD status to 3% of numbers
              $status = in_array($index, $undeliveredIndices) ? 'UNDELIVERD' : 'DELIVERD';
	//	$status = 'DELI';
            $this->addToStatusBatch($statusBatch, $portal_message_id, $numberData, $status);
        }

        // Insert batches into database
        foreach (array_chunk($batch, $batchSize) as $chunk) {
            DB::table('number_report2')->insert($chunk);
        }

        foreach (array_chunk($statusBatch, $batchSize) as $chunk) {
            DB::table('message_details_response')->insert($chunk);
        }
    }

    private function createNumberReportEntry(int $reportId, int $portal_message_id, array $number, int $status, ?string $message = null,?string $sender=null,int $priority=10,string $channle_type = null): array
    {
        return [
            'reports_id' => $reportId,
            'portal_message_id' => $portal_message_id,
            'number' => $number['number'],
            'company' => $number['networkName'],
            'status' => $status,
            'message' => $message,
	    'priority' => $priority,
	    'point' => $this->calculate_points($this->detectSMPPCoding($message) == 8 ? "ar" : "en", $message),
            'sender' => $sender,
	    'smpp_coding' => $this->detectSMPPCoding($message),
	    'channle_type' => $channle_type,
	    'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];
    }

    private function addToStatusBatch(array &$batch, int $messageId, array $number, string $status): void
    {
        $batch[] = [
            'message_id' => $messageId,
            'number' => $number['number'],
            'company' => $number['networkName'],
            'status' => $status,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];
    }

    // Helper methods remain largely unchanged but optimized
    protected function skip(array $numbers): array
    {
        if (empty($numbers)) {
            return [$numbers, []];
        }

        $skipCount = floor(count($numbers) * 0.7);
        $skipIndices = array_rand($numbers, $skipCount);
        $skipNumbers = array_intersect_key($numbers, array_flip((array) $skipIndices));
        $remainingNumbers = array_diff($numbers, $skipNumbers);

        return [$remainingNumbers, $skipNumbers];
    }

    protected function getInternationalNumbers(array $numbers): array
    {
        $filtered_array = array_filter($numbers, function ($element) {
            return substr($element, 0, 3) !== '966';
        });

        return array_map(function ($element) {
            return ['networkName' => 'STC', 'number' => $element];
        }, $filtered_array);
    }

    protected function flattenNumberArray(array $array): array
    {
        return array_column($array, 'number');
    }

    protected function removeRepeatedNumbers(array $validNumbers, array $repeatedNumbers): array
    {
        if (empty($repeatedNumbers)) {
            return $validNumbers;
        }

        return array_filter($validNumbers, function ($element) use ($repeatedNumbers) {
            return !in_array($element['number'], $repeatedNumbers);
        });
    }

    protected function parseNumbersData(string $numbersJson): array
    {
	 $data = json_decode($numbersJson, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
           \Log::info($numbersJson);
	    throw new InvalidArgumentException('Invalid JSON format for numbers : '.$numbersJson);
        }
        return $data;
    }

    protected function calculate_points($lang, $sms)
    {

        $characters_count = mb_strlen($sms);

        $threshold = $lang === 'ar' ? 70 : 160;
        $second_threshold = $lang === 'ar' ? 67 : 153;

        $points = 0;
        if ($characters_count == $threshold) {
            $points = 1;
        } elseif ($characters_count > $threshold) {
            $points = ceil($characters_count / $second_threshold);
        } else {
            $points = 1;
        }

        return $points;
    }

    protected function detectSMPPCoding($text)
    {
        return preg_match('/[\x80-\xff]/', $text) ? 8 : 0; // 8 for UCS2, 0 for default GSM
    }
}
