<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Failed_job_model extends CI_Model {

    protected $table = 'portal_failed_jobs';

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Save a failed job to the database
     */
    public function save_failed_job($job_data) {
        $data = [
            'uuid' => $this->generate_uuid(),
            'connection' => isset($job_data['connection']) ? $job_data['connection'] : 'redis',
            'queue' => isset($job_data['queue']) ? $job_data['queue'] : 'default',
            'payload' => json_encode($job_data['payload']),
            'exception' => $job_data['exception'],
            'job_class' => $job_data['job_class'],
            'job_data' => isset($job_data['job_data']) ? json_encode($job_data['job_data']) : null,
            'attempts' => isset($job_data['attempts']) ? $job_data['attempts'] : 1,
            'error_message' => isset($job_data['error_message']) ? $job_data['error_message'] : null,
            'error_file' => isset($job_data['error_file']) ? $job_data['error_file'] : null,
            'error_line' => isset($job_data['error_line']) ? $job_data['error_line'] : null,
            'stack_trace' => isset($job_data['stack_trace']) ? $job_data['stack_trace'] : null,
            'worker_id' => isset($job_data['worker_id']) ? $job_data['worker_id'] : null,
            'failed_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert($this->table, $data);
    }

    /**
     * Get failed jobs with pagination and filtering
     */
    public function get_failed_jobs($limit = 50, $offset = 0, $filters = []) {
        $this->db->select('*');
        $this->db->from($this->table);

        // Apply filters
        if (!empty($filters['queue'])) {
            $this->db->where('queue', $filters['queue']);
        }

        if (!empty($filters['job_class'])) {
            $this->db->where('job_class', $filters['job_class']);
        }

        if (!empty($filters['date_from'])) {
            $this->db->where('failed_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $this->db->where('failed_at <=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $this->db->group_start();
            $this->db->like('job_class', $filters['search']);
            $this->db->or_like('error_message', $filters['search']);
            $this->db->or_like('queue', $filters['search']);
            $this->db->group_end();
        }

        $this->db->order_by('failed_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result_array();
    }

    /**
     * Count failed jobs with filters
     */
    public function count_failed_jobs($filters = []) {
        $this->db->from($this->table);

        // Apply same filters as get_failed_jobs
        if (!empty($filters['queue'])) {
            $this->db->where('queue', $filters['queue']);
        }

        if (!empty($filters['job_class'])) {
            $this->db->where('job_class', $filters['job_class']);
        }

        if (!empty($filters['date_from'])) {
            $this->db->where('failed_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $this->db->where('failed_at <=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $this->db->group_start();
            $this->db->like('job_class', $filters['search']);
            $this->db->or_like('error_message', $filters['search']);
            $this->db->or_like('queue', $filters['search']);
            $this->db->group_end();
        }

        return $this->db->count_all_results();
    }

    /**
     * Get a specific failed job by ID
     */
    public function get_failed_job($id) {
        return $this->db->where('id', $id)->get($this->table)->row_array();
    }

    /**
     * Get a specific failed job by UUID
     */
    public function get_failed_job_by_uuid($uuid) {
        return $this->db->where('uuid', $uuid)->get($this->table)->row_array();
    }

    /**
     * Delete a failed job
     */
    public function delete_failed_job($id) {
        return $this->db->where('id', $id)->delete($this->table);
    }

    /**
     * Delete failed jobs older than specified days
     */
    public function delete_old_failed_jobs($days = 30) {
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return $this->db->where('failed_at <', $cutoff_date)->delete($this->table);
    }

    /**
     * Get failed jobs statistics
     */
    public function get_statistics($days = 7) {
        $date_from = date('Y-m-d H:i:s', strtotime("-{$days} days"));

        // Total failed jobs in period
        $total = $this->db->where('failed_at >=', $date_from)->count_all_results($this->table);

        // Failed jobs by queue
        $by_queue = $this->db->select('queue, COUNT(*) as count')
                            ->where('failed_at >=', $date_from)
                            ->group_by('queue')
                            ->order_by('count', 'DESC')
                            ->get($this->table)
                            ->result_array();

        // Failed jobs by job class
        $by_job_class = $this->db->select('job_class, COUNT(*) as count')
                                ->where('failed_at >=', $date_from)
                                ->group_by('job_class')
                                ->order_by('count', 'DESC')
                                ->get($this->table)
                                ->result_array();

        // Failed jobs by day
        $by_day = $this->db->select('DATE(failed_at) as date, COUNT(*) as count')
                          ->where('failed_at >=', $date_from)
                          ->group_by('DATE(failed_at)')
                          ->order_by('date', 'ASC')
                          ->get($this->table)
                          ->result_array();

        return [
            'total' => $total,
            'by_queue' => $by_queue,
            'by_job_class' => $by_job_class,
            'by_day' => $by_day,
            'period_days' => $days
        ];
    }

    /**
     * Get most common error messages
     */
    public function get_common_errors($limit = 10) {
        return $this->db->select('error_message, COUNT(*) as count')
                       ->where('error_message IS NOT NULL')
                       ->group_by('error_message')
                       ->order_by('count', 'DESC')
                       ->limit($limit)
                       ->get($this->table)
                       ->result_array();
    }

    /**
     * Clear all failed jobs from database
     */
    public function clear_all_failed_jobs() {
        return $this->db->empty_table($this->table);
    }

    /**
     * Generate a UUID for the failed job
     */
    private function generate_uuid() {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get failed jobs export data
     */
    public function get_export_data($filters = []) {
        $this->db->select('uuid, queue, job_class, error_message, attempts, failed_at, job_data');
        $this->db->from($this->table);

        // Apply filters (same as get_failed_jobs)
        if (!empty($filters['queue'])) {
            $this->db->where('queue', $filters['queue']);
        }

        if (!empty($filters['job_class'])) {
            $this->db->where('job_class', $filters['job_class']);
        }

        if (!empty($filters['date_from'])) {
            $this->db->where('failed_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $this->db->where('failed_at <=', $filters['date_to']);
        }

        $this->db->order_by('failed_at', 'DESC');

        return $this->db->get()->result_array();
    }
}
