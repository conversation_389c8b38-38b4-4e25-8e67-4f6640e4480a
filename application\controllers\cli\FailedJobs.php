<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FailedJobs extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Only allow CLI access
        if (!$this->input->is_cli_request()) {
            show_error('This script can only be accessed via command line.');
        }
        
        $this->load->library('redisqueue');
        $this->load->model('message');
    }

    /**
     * Show help information
     */
    public function index() {
        $this->show_help();
    }

    /**
     * List all failed jobs from Redis queue
     * Usage: php index.php cli/FailedJobs list [queue_name]
     */
    public function list($queue = 'default') {
        $this->console_log("=== Failed Jobs in Queue: {$queue} ===", 'info');
        
        $failedJobs = $this->redisqueue->getFailedJobs($queue);
        
        if (empty($failedJobs)) {
            $this->console_log("No failed jobs found in queue '{$queue}'", 'success');
            return;
        }

        $this->console_log("Found " . count($failedJobs) . " failed jobs:", 'warning');
        $this->console_log("");

        foreach ($failedJobs as $job) {
            $this->console_log("Job ID: {$job['id']}", 'info');
            $this->console_log("  Job Class: {$job['job']}", 'info');
            $this->console_log("  Attempts: {$job['attempts']}", 'info');
            $this->console_log("  Created: {$job['created_at']}", 'info');
            $this->console_log("  Data: " . json_encode($job['data']), 'info');
            $this->console_log("  " . str_repeat("-", 50), 'info');
        }
    }

    /**
     * Show statistics for all queues
     * Usage: php index.php cli/FailedJobs stats [queue_name]
     */
    public function stats($queue = null) {
        if ($queue) {
            $this->show_queue_stats($queue);
        } else {
            // Show stats for common queues
            $queues = ['default', 'sms', 'email', 'notifications'];
            
            $this->console_log("=== Queue Statistics ===", 'info');
            $this->console_log("");
            
            foreach ($queues as $q) {
                $stats = $this->redisqueue->getQueueStats($q);
                if (array_sum($stats) > 0) { // Only show queues with jobs
                    $this->show_queue_stats($q);
                }
            }
        }
    }

    /**
     * Show detailed information about a specific failed job
     * Usage: php index.php cli/FailedJobs show <job_id> [queue_name]
     */
    public function show($jobId, $queue = 'default') {
        if (empty($jobId)) {
            $this->console_log("Error: Job ID is required", 'error');
            $this->console_log("Usage: php index.php cli/FailedJobs show <job_id> [queue_name]", 'info');
            return;
        }

        $job = $this->redisqueue->getFailedJob($jobId, $queue);
        
        if (!$job) {
            $this->console_log("Failed job with ID '{$jobId}' not found in queue '{$queue}'", 'error');
            return;
        }

        $this->console_log("=== Failed Job Details ===", 'info');
        $this->console_log("Job ID: {$job['id']}", 'info');
        $this->console_log("Job Class: {$job['job']}", 'info');
        $this->console_log("Queue: {$queue}", 'info');
        $this->console_log("Attempts: {$job['attempts']}", 'info');
        $this->console_log("Created At: {$job['created_at']}", 'info');
        $this->console_log("Job Data:", 'info');
        $this->console_log(json_encode($job['data'], JSON_PRETTY_PRINT), 'info');
    }

    /**
     * Retry a specific failed job
     * Usage: php index.php cli/FailedJobs retry <job_id> [queue_name]
     */
    public function retry($jobId, $queue = 'default') {
        if (empty($jobId)) {
            $this->console_log("Error: Job ID is required", 'error');
            $this->console_log("Usage: php index.php cli/FailedJobs retry <job_id> [queue_name]", 'info');
            return;
        }

        $result = $this->redisqueue->retryFailedJob($jobId, $queue);
        
        if ($result) {
            $this->console_log("✓ Job '{$jobId}' has been moved back to queue '{$queue}' for retry", 'success');
        } else {
            $this->console_log("✗ Failed to retry job '{$jobId}' - job not found", 'error');
        }
    }

    /**
     * Retry all failed jobs in a queue
     * Usage: php index.php cli/FailedJobs retry_all [queue_name]
     */
    public function retry_all($queue = 'default') {
        $failedJobs = $this->redisqueue->getFailedJobs($queue);
        
        if (empty($failedJobs)) {
            $this->console_log("No failed jobs found in queue '{$queue}'", 'success');
            return;
        }

        $this->console_log("Retrying " . count($failedJobs) . " failed jobs...", 'info');
        
        $retried = 0;
        foreach ($failedJobs as $job) {
            if ($this->redisqueue->retryFailedJob($job['id'], $queue)) {
                $retried++;
                $this->console_log("✓ Retried job: {$job['id']}", 'success');
            } else {
                $this->console_log("✗ Failed to retry job: {$job['id']}", 'error');
            }
        }
        
        $this->console_log("", 'info');
        $this->console_log("Retried {$retried} out of " . count($failedJobs) . " failed jobs", 'info');
    }

    /**
     * Delete a specific failed job
     * Usage: php index.php cli/FailedJobs delete <job_id> [queue_name]
     */
    public function delete($jobId, $queue = 'default') {
        if (empty($jobId)) {
            $this->console_log("Error: Job ID is required", 'error');
            $this->console_log("Usage: php index.php cli/FailedJobs delete <job_id> [queue_name]", 'info');
            return;
        }

        $result = $this->redisqueue->deleteFailedJob($jobId, $queue);
        
        if ($result) {
            $this->console_log("✓ Failed job '{$jobId}' has been deleted from queue '{$queue}'", 'success');
        } else {
            $this->console_log("✗ Failed to delete job '{$jobId}' - job not found", 'error');
        }
    }

    /**
     * Clear all failed jobs from a queue
     * Usage: php index.php cli/FailedJobs clear [queue_name]
     */
    public function clear($queue = 'default') {
        $count = $this->redisqueue->getFailedJobsCount($queue);
        
        if ($count == 0) {
            $this->console_log("No failed jobs found in queue '{$queue}'", 'success');
            return;
        }

        $this->console_log("Warning: This will delete {$count} failed jobs from queue '{$queue}'", 'warning');
        $this->console_log("Type 'yes' to confirm: ", 'warning');
        
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($confirmation) === 'yes') {
            $result = $this->redisqueue->clearFailedJobs($queue);
            if ($result) {
                $this->console_log("✓ All failed jobs cleared from queue '{$queue}'", 'success');
            } else {
                $this->console_log("✗ Failed to clear jobs from queue '{$queue}'", 'error');
            }
        } else {
            $this->console_log("Operation cancelled", 'info');
        }
    }

    /**
     * Check failed messages from database (legacy system)
     * Usage: php index.php cli/FailedJobs check_db_failed
     */
    public function check_db_failed() {
        $this->console_log("=== Database Failed Messages ===", 'info');
        
        $failed_messages = $this->message->check_faild();
        
        if (!empty($failed_messages) && isset($failed_messages[0]['total_faild'])) {
            $count = $failed_messages[0]['total_faild'];
            $this->console_log("Total failed messages in database: {$count}", 'warning');
        } else {
            $this->console_log("No failed messages found in database", 'success');
        }
    }

    /**
     * Show help information
     */
    private function show_help() {
        $this->console_log("=== Failed Jobs Management CLI ===", 'info');
        $this->console_log("", 'info');
        $this->console_log("Available commands:", 'info');
        $this->console_log("", 'info');
        $this->console_log("  list [queue]           - List all failed jobs in queue (default: 'default')", 'info');
        $this->console_log("  stats [queue]          - Show queue statistics", 'info');
        $this->console_log("  show <job_id> [queue]  - Show detailed job information", 'info');
        $this->console_log("  retry <job_id> [queue] - Retry a specific failed job", 'info');
        $this->console_log("  retry_all [queue]      - Retry all failed jobs in queue", 'info');
        $this->console_log("  delete <job_id> [queue]- Delete a specific failed job", 'info');
        $this->console_log("  clear [queue]          - Clear all failed jobs from queue", 'info');
        $this->console_log("  check_db_failed        - Check failed messages from database", 'info');
        $this->console_log("", 'info');
        $this->console_log("Examples:", 'info');
        $this->console_log("  php index.php cli/FailedJobs list", 'info');
        $this->console_log("  php index.php cli/FailedJobs stats sms", 'info');
        $this->console_log("  php index.php cli/FailedJobs retry job_12345", 'info');
        $this->console_log("  php index.php cli/FailedJobs clear default", 'info');
    }

    /**
     * Show statistics for a specific queue
     */
    private function show_queue_stats($queue) {
        $stats = $this->redisqueue->getQueueStats($queue);
        
        $this->console_log("Queue: {$queue}", 'info');
        $this->console_log("  Pending: {$stats['pending']}", 'info');
        $this->console_log("  Processing: {$stats['processing']}", 'info');
        $this->console_log("  Failed: {$stats['failed']}", ($stats['failed'] > 0 ? 'warning' : 'success'));
        $this->console_log("  Delayed: {$stats['delayed']}", 'info');
        $this->console_log("");
    }

    /**
     * Console logging with colors
     */
    private function console_log($message, $type = 'info') {
        $date = date('Y-m-d H:i:s');
        
        // ANSI color codes
        $colors = [
            'info' => "\033[0m",     // Default
            'success' => "\033[32m",  // Green
            'error' => "\033[31m",    // Red
            'warning' => "\033[33m"   // Yellow
        ];
        
        $color = isset($colors[$type]) ? $colors[$type] : $colors['info'];
        $reset = "\033[0m";
        
        // Don't add timestamp for empty lines
        if (empty(trim($message))) {
            echo PHP_EOL;
        } else {
            echo "{$color}[{$date}] {$message}{$reset}" . PHP_EOL;
        }
    }
}
