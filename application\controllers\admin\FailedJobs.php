<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FailedJobs extends MY_Controller {
var $entry_view = "admin/index";
    public function __construct() {
        parent::__construct();

        // Check admin authentication
        if (!$this->session->userdata('admin_logged_in')) {
            redirect('admin/login');
        }

        $this->load->library('RedisQueue');
        $this->load->model('message');
        $this->load->model('Failed_job_model');
    }

    /**
     * Main failed jobs dashboard
     */
    public function index() {
        $data['page_title'] = 'Failed Jobs Management';

        // Get statistics for all queues
        $queues = ['default', 'sms', 'email', 'notifications'];
        $data['queue_stats'] = [];

        foreach ($queues as $queue) {
            $stats = $this->redisqueue->getQueueStats($queue);
            if (array_sum($stats) > 0) { // Only include queues with jobs
                $data['queue_stats'][$queue] = $stats;
            }
        }

        // Get database failed messages count (legacy)
        $failed_messages = $this->message->check_faild();
        $data['db_failed_count'] = isset($failed_messages[0]['total_faild']) ? $failed_messages[0]['total_faild'] : 0;

        // Get database failed jobs count (new system)
        $data['db_failed_jobs_count'] = $this->Failed_job_model->count_failed_jobs();

        // Get recent failed jobs statistics
        $data['failed_jobs_stats'] = $this->Failed_job_model->get_statistics(30);
            $this->page_data['inner_page_data'] = $data;
         $this->page_data['view'] = 'admin/failed_jobs_dashboard';  
          $this->load_view();
    }

    /**
     * Get failed jobs data for AJAX DataTable
     */
    public function load_data() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['error' => '401']);
            return;
        }

        $queue = $this->input->post('queue') ?: 'default';
        $failedJobs = $this->redisqueue->getFailedJobs($queue);

        // Prepare data for DataTable
        $data = [];
        foreach ($failedJobs as $job) {
            $data[] = [
                'id' => $job['id'],
                'job' => $job['job'],
                'attempts' => $job['attempts'],
                'created_at' => $job['created_at'],
                'data' => json_encode($job['data']),
                'actions' => $this->generate_action_buttons($job['id'], $queue)
            ];
        }

        echo json_encode([
            'draw' => intval($this->input->post('draw')),
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $data
        ]);
    }

    /**
     * Get database failed jobs data for AJAX DataTable
     */
    public function load_database_data() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['error' => '401']);
            return;
        }

        $limit = intval($this->input->post('length')) ?: 50;
        $offset = intval($this->input->post('start')) ?: 0;

        // Get filters
        $filters = [];
        if ($this->input->post('queue')) {
            $filters['queue'] = $this->input->post('queue');
        }
        if ($this->input->post('search')['value']) {
            $filters['search'] = $this->input->post('search')['value'];
        }

        $failed_jobs = $this->Failed_job_model->get_failed_jobs($limit, $offset, $filters);
        $total_count = $this->Failed_job_model->count_failed_jobs($filters);

        // Prepare data for DataTable
        $data = [];
        foreach ($failed_jobs as $job) {
            $data[] = [
                'id' => $job['id'],
                'uuid' => $job['uuid'],
                'job_class' => $job['job_class'],
                'queue' => $job['queue'],
                'attempts' => $job['attempts'],
                'failed_at' => $job['failed_at'],
                'error_message' => substr($job['error_message'] ?? 'No error message', 0, 100),
                'actions' => $this->generate_database_action_buttons($job['id'])
            ];
        }

        echo json_encode([
            'draw' => intval($this->input->post('draw')),
            'recordsTotal' => $total_count,
            'recordsFiltered' => $total_count,
            'data' => $data
        ]);
    }

    /**
     * Retry a failed job via AJAX
     */
    public function retry_job() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $jobId = $this->input->post('job_id');
        $queue = $this->input->post('queue') ?: 'default';

        if (empty($jobId)) {
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        $result = $this->redisqueue->retryFailedJob($jobId, $queue);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Job retried successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to retry job']);
        }
    }

    /**
     * Delete a failed job via AJAX
     */
    public function delete_job() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $jobId = $this->input->post('job_id');
        $queue = $this->input->post('queue') ?: 'default';

        if (empty($jobId)) {
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        $result = $this->redisqueue->deleteFailedJob($jobId, $queue);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Job deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete job']);
        }
    }

    /**
     * Clear all failed jobs in a queue
     */
    public function clear_queue() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $queue = $this->input->post('queue') ?: 'default';
        $count = $this->redisqueue->getFailedJobsCount($queue);

        if ($count == 0) {
            echo json_encode(['success' => true, 'message' => 'No failed jobs to clear']);
            return;
        }

        $result = $this->redisqueue->clearFailedJobs($queue);

        if ($result) {
            echo json_encode(['success' => true, 'message' => "Cleared {$count} failed jobs from queue '{$queue}'"]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to clear jobs']);
        }
    }

    /**
     * Retry all failed jobs in a queue
     */
    public function retry_all() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $queue = $this->input->post('queue') ?: 'default';
        $failedJobs = $this->redisqueue->getFailedJobs($queue);

        if (empty($failedJobs)) {
            echo json_encode(['success' => true, 'message' => 'No failed jobs to retry']);
            return;
        }

        $retried = 0;
        foreach ($failedJobs as $job) {
            if ($this->redisqueue->retryFailedJob($job['id'], $queue)) {
                $retried++;
            }
        }

        echo json_encode([
            'success' => true,
            'message' => "Retried {$retried} out of " . count($failedJobs) . " failed jobs"
        ]);
    }

    /**
     * Get job details for modal view
     */
    public function get_job_details() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $jobId = $this->input->post('job_id');
        $queue = $this->input->post('queue') ?: 'default';

        if (empty($jobId)) {
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        $job = $this->redisqueue->getFailedJob($jobId, $queue);

        if ($job) {
            echo json_encode(['success' => true, 'job' => $job]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Job not found']);
        }
    }

    /**
     * Generate action buttons for each job row
     */
    private function generate_action_buttons($jobId, $queue) {
        $buttons = '';

        // View details button
        $buttons .= '<button class="btn btn-sm btn-info view-job" data-job-id="' . $jobId . '" data-queue="' . $queue . '" title="View Details">';
        $buttons .= '<i class="fa fa-eye"></i>';
        $buttons .= '</button> ';

        // Retry button
        $buttons .= '<button class="btn btn-sm btn-warning retry-job" data-job-id="' . $jobId . '" data-queue="' . $queue . '" title="Retry Job">';
        $buttons .= '<i class="fa fa-refresh"></i>';
        $buttons .= '</button> ';

        // Delete button
        $buttons .= '<button class="btn btn-sm btn-danger delete-job" data-job-id="' . $jobId . '" data-queue="' . $queue . '" title="Delete Job">';
        $buttons .= '<i class="fa fa-trash"></i>';
        $buttons .= '</button>';

        return $buttons;
    }

    /**
     * Generate action buttons for database failed jobs
     */
    private function generate_database_action_buttons($jobId) {
        $buttons = '';

        // View details button
        $buttons .= '<button class="btn btn-sm btn-info view-db-job" data-job-id="' . $jobId . '" title="View Details">';
        $buttons .= '<i class="fa fa-eye"></i>';
        $buttons .= '</button> ';

        // Delete button
        $buttons .= '<button class="btn btn-sm btn-danger delete-db-job" data-job-id="' . $jobId . '" title="Delete Job">';
        $buttons .= '<i class="fa fa-trash"></i>';
        $buttons .= '</button>';

        return $buttons;
    }

    /**
     * Get database job details for modal view
     */
    public function get_database_job_details() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $jobId = $this->input->post('job_id');

        if (empty($jobId)) {
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        $job = $this->Failed_job_model->get_failed_job($jobId);

        if ($job) {
            echo json_encode(['success' => true, 'job' => $job]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Job not found']);
        }
    }

    /**
     * Delete a database failed job via AJAX
     */
    public function delete_database_job() {
        if (!$this->session->userdata('admin_logged_in')) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            return;
        }

        $jobId = $this->input->post('job_id');

        if (empty($jobId)) {
            echo json_encode(['success' => false, 'message' => 'Job ID is required']);
            return;
        }

        $result = $this->Failed_job_model->delete_failed_job($jobId);

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Job deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete job']);
        }
    }

    /**
     * Export failed jobs to CSV
     */
    public function export_csv() {
        if (!$this->session->userdata('admin_logged_in')) {
            show_404();
            return;
        }

        $queue = $this->input->get('queue') ?: 'default';
        $failedJobs = $this->redisqueue->getFailedJobs($queue);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="failed_jobs_' . $queue . '_' . date('Y-m-d_H-i-s') . '.csv"');

        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, ['Job ID', 'Job Class', 'Attempts', 'Created At', 'Job Data']);

        // CSV data
        foreach ($failedJobs as $job) {
            fputcsv($output, [
                $job['id'],
                $job['job'],
                $job['attempts'],
                $job['created_at'],
                json_encode($job['data'])
            ]);
        }

        fclose($output);
    }
}
