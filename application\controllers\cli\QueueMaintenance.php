<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class QueueMaintenance extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Only allow CLI access
        if (!$this->input->is_cli_request()) {
            show_error('This script can only be accessed via command line.');
        }
        
        $this->load->library('redisqueue');
    }

    /**
     * Automatic maintenance routine for queue cleanup
     * Usage: php index.php cli/QueueMaintenance auto_cleanup
     */
    public function auto_cleanup() {
        $this->console_log("=== Queue Maintenance Started ===", 'info');
        
        // Define queues to check
        $queues = ['default', 'sms', 'email', 'notifications'];
        $total_cleaned = 0;
        
        foreach ($queues as $queue) {
            $this->console_log("Checking queue: {$queue}", 'info');
            
            // Clean up stuck jobs (jobs processing for more than 30 minutes)
            $cleaned = $this->redisqueue->cleanupStuckJobs(30, $queue);
            
            if ($cleaned > 0) {
                $this->console_log("  ✓ Moved {$cleaned} stuck jobs to failed queue", 'warning');
                $total_cleaned += $cleaned;
            } else {
                $this->console_log("  ✓ No stuck jobs found", 'success');
            }
            
            // Log queue statistics
            $stats = $this->redisqueue->getQueueStats($queue);
            if (array_sum($stats) > 0) {
                $this->console_log("  Stats - Pending: {$stats['pending']}, Processing: {$stats['processing']}, Failed: {$stats['failed']}, Delayed: {$stats['delayed']}", 'info');
            }
        }
        
        $this->console_log("", 'info');
        $this->console_log("=== Maintenance Complete ===", 'info');
        $this->console_log("Total stuck jobs cleaned: {$total_cleaned}", $total_cleaned > 0 ? 'warning' : 'success');
        
        // Log to file for monitoring
        $this->log_maintenance_result($total_cleaned);
    }

    /**
     * Clean up old failed jobs (older than specified days)
     * Usage: php index.php cli/QueueMaintenance cleanup_old_failed [days] [queue]
     */
    public function cleanup_old_failed($days = 7, $queue = 'default') {
        $this->console_log("=== Cleaning Old Failed Jobs ===", 'info');
        $this->console_log("Queue: {$queue}", 'info');
        $this->console_log("Older than: {$days} days", 'info');
        
        $failedJobs = $this->redisqueue->getFailedJobs($queue);
        $cutoff_time = time() - ($days * 24 * 60 * 60);
        $deleted = 0;
        
        foreach ($failedJobs as $job) {
            $job_time = strtotime($job['created_at']);
            if ($job_time < $cutoff_time) {
                if ($this->redisqueue->deleteFailedJob($job['id'], $queue)) {
                    $deleted++;
                }
            }
        }
        
        $this->console_log("✓ Deleted {$deleted} old failed jobs", 'success');
    }

    /**
     * Monitor queue health and send alerts if needed
     * Usage: php index.php cli/QueueMaintenance health_check
     */
    public function health_check() {
        $this->console_log("=== Queue Health Check ===", 'info');
        
        $queues = ['default', 'sms', 'email', 'notifications'];
        $alerts = [];
        
        foreach ($queues as $queue) {
            $stats = $this->redisqueue->getQueueStats($queue);
            
            // Check for high number of failed jobs
            if ($stats['failed'] > 50) {
                $alerts[] = "Queue '{$queue}' has {$stats['failed']} failed jobs";
            }
            
            // Check for stuck processing jobs
            $processingJobs = $this->redisqueue->getProcessingJobs($queue);
            $stuck_count = 0;
            
            foreach ($processingJobs as $job) {
                $processing_time = time() - strtotime($job['created_at']);
                if ($processing_time > 1800) { // 30 minutes
                    $stuck_count++;
                }
            }
            
            if ($stuck_count > 0) {
                $alerts[] = "Queue '{$queue}' has {$stuck_count} jobs stuck in processing";
            }
            
            // Check for very high pending jobs
            if ($stats['pending'] > 1000) {
                $alerts[] = "Queue '{$queue}' has {$stats['pending']} pending jobs (high load)";
            }
            
            $this->console_log("Queue {$queue}: Pending={$stats['pending']}, Processing={$stats['processing']}, Failed={$stats['failed']}, Delayed={$stats['delayed']}", 'info');
        }
        
        if (!empty($alerts)) {
            $this->console_log("", 'warning');
            $this->console_log("⚠️  ALERTS DETECTED:", 'warning');
            foreach ($alerts as $alert) {
                $this->console_log("  - {$alert}", 'warning');
            }
            
            // Log alerts for external monitoring
            $this->log_alerts($alerts);
        } else {
            $this->console_log("✓ All queues are healthy", 'success');
        }
    }

    /**
     * Emergency cleanup - clear all stuck processing jobs
     * Usage: php index.php cli/QueueMaintenance emergency_cleanup
     */
    public function emergency_cleanup() {
        $this->console_log("=== EMERGENCY CLEANUP ===", 'error');
        $this->console_log("This will move ALL processing jobs to failed queue!", 'warning');
        $this->console_log("Only use this if you're sure no workers are running!", 'warning');
        $this->console_log("Type 'EMERGENCY' to confirm: ", 'warning');
        
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);
        
        if ($confirmation === 'EMERGENCY') {
            $queues = ['default', 'sms', 'email', 'notifications'];
            $total_moved = 0;
            
            foreach ($queues as $queue) {
                $processingJobs = $this->redisqueue->getProcessingJobs($queue);
                $moved = 0;
                
                foreach ($processingJobs as $job) {
                    if ($this->redisqueue->forceFailProcessingJob($job['id'], $queue)) {
                        $moved++;
                    }
                }
                
                if ($moved > 0) {
                    $this->console_log("Queue '{$queue}': Moved {$moved} jobs to failed", 'warning');
                    $total_moved += $moved;
                }
            }
            
            $this->console_log("Emergency cleanup complete. Total jobs moved: {$total_moved}", 'error');
            $this->log_emergency_cleanup($total_moved);
        } else {
            $this->console_log("Emergency cleanup cancelled", 'info');
        }
    }

    /**
     * Log maintenance results to file
     */
    private function log_maintenance_result($cleaned_count) {
        $log_entry = date('Y-m-d H:i:s') . " - Queue maintenance: {$cleaned_count} stuck jobs cleaned\n";
        file_put_contents(APPPATH . 'logs/queue_maintenance.log', $log_entry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log alerts to file
     */
    private function log_alerts($alerts) {
        $log_entry = date('Y-m-d H:i:s') . " - Queue alerts:\n";
        foreach ($alerts as $alert) {
            $log_entry .= "  - {$alert}\n";
        }
        $log_entry .= "\n";
        
        file_put_contents(APPPATH . 'logs/queue_alerts.log', $log_entry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log emergency cleanup to file
     */
    private function log_emergency_cleanup($moved_count) {
        $log_entry = date('Y-m-d H:i:s') . " - EMERGENCY CLEANUP: {$moved_count} jobs moved to failed\n";
        file_put_contents(APPPATH . 'logs/queue_emergency.log', $log_entry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Console logging with colors
     */
    private function console_log($message, $type = 'info') {
        $date = date('Y-m-d H:i:s');
        
        // ANSI color codes
        $colors = [
            'info' => "\033[0m",     // Default
            'success' => "\033[32m",  // Green
            'error' => "\033[31m",    // Red
            'warning' => "\033[33m"   // Yellow
        ];
        
        $color = isset($colors[$type]) ? $colors[$type] : $colors['info'];
        $reset = "\033[0m";
        
        // Don't add timestamp for empty lines
        if (empty(trim($message))) {
            echo PHP_EOL;
        } else {
            echo "{$color}[{$date}] {$message}{$reset}" . PHP_EOL;
        }
    }
}
