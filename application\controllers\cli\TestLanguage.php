<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TestLanguage extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Only allow CLI access
        if (!$this->input->is_cli_request()) {
            show_error('This script can only be accessed via command line.');
        }
    }

    /**
     * Test language loading functionality
     * Usage: php index.php cli/TestLanguage test_lang_loading
     */
    public function test_lang_loading() {
        echo "=== Testing Language Loading ===\n";
        
        try {
            // Test loading the lang language file (which contains message strings)
            $this->load->language('lang');
            echo "✓ Successfully loaded 'lang' language file\n";
            
            // Test accessing a message-related language string
            $test_key = 'msg_error_insufficient_balance';
            $message = $this->lang->line($test_key);
            
            if ($message) {
                echo "✓ Successfully retrieved language string: '{$test_key}' = '{$message}'\n";
            } else {
                echo "✗ Failed to retrieve language string: '{$test_key}'\n";
            }
            
            // Test a few more common message keys
            $test_keys = [
                'msg_error_login',
                'msg_success_saving',
                'msg_error_required_field'
            ];
            
            foreach ($test_keys as $key) {
                $value = $this->lang->line($key);
                if ($value) {
                    echo "✓ Key '{$key}': '{$value}'\n";
                } else {
                    echo "✗ Key '{$key}': NOT FOUND\n";
                }
            }
            
        } catch (Exception $e) {
            echo "✗ Error loading language: " . $e->getMessage() . "\n";
        }
        
        echo "\n=== Test Complete ===\n";
    }

    /**
     * Test SmsProcessing job initialization
     * Usage: php index.php cli/TestLanguage test_sms_processing
     */
    public function test_sms_processing() {
        echo "=== Testing SmsProcessing Initialization ===\n";
        
        try {
            // Load the SmsProcessing class
            require_once APPPATH . 'jobs/SmsProcessing.php';
            
            // Create an instance
            $smsProcessing = new SmsProcessing();
            
            // Use reflection to call the protected initialize method
            $reflection = new ReflectionClass($smsProcessing);
            $initializeMethod = $reflection->getMethod('initialize');
            $initializeMethod->setAccessible(true);
            
            // Call the initialize method
            $initializeMethod->invoke($smsProcessing);
            
            echo "✓ SmsProcessing class initialized successfully\n";
            echo "✓ All dependencies loaded without errors\n";
            
        } catch (Exception $e) {
            echo "✗ Error initializing SmsProcessing: " . $e->getMessage() . "\n";
            echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        }
        
        echo "\n=== Test Complete ===\n";
    }

    /**
     * Test all language files
     * Usage: php index.php cli/TestLanguage test_all_languages
     */
    public function test_all_languages() {
        echo "=== Testing All Language Files ===\n";
        
        $languages = ['english', 'arabic'];
        
        foreach ($languages as $lang) {
            echo "\nTesting {$lang} language:\n";
            
            try {
                $this->load->language('lang', $lang);
                echo "✓ Successfully loaded '{$lang}' language file\n";
                
                // Test a common key
                $test_message = $this->lang->line('msg_error_insufficient_balance');
                if ($test_message) {
                    echo "✓ Retrieved test message: '{$test_message}'\n";
                } else {
                    echo "✗ Could not retrieve test message\n";
                }
                
            } catch (Exception $e) {
                echo "✗ Error loading {$lang} language: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n=== Test Complete ===\n";
    }
}
