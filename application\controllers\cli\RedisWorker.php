<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RedisWorker extends CI_Controller {
    public function __construct() {
        parent::__construct();

        // Make sure this can only be run from CLI
        if (!is_cli()) {
            exit("This script can only be accessed via CLI");
        }

        // Disable output buffering
        if (ob_get_level()) ob_end_clean();

        // Turn off output buffering
        ob_implicit_flush(true);
    }

    public function index($queue = 'default') {
        $this->load->library('redisqueue');

        // Clear the screen first (works in both Windows and Unix)
        system('clear');
        system('cls');

        $this->output->set_header('Content-Type: text/plain');

        // Print worker start message
        $this->console_log("Starting worker for queue: {$queue}");
        $this->console_log("Press Ctrl+C to stop the worker\n");

        while (true) {
            $job = $this->redisqueue->getNextJob($queue);

            if ($job) {
                try {
                    $this->console_log("Processing job: {$job->job}");

                    // Load and execute the job
                    require_once APPPATH . 'jobs/' . $job->job . '.php';
                    $jobInstance = new $job->job();

                    // Log job details
                    $this->console_log("Job ID: {$job->id}");
                    $this->console_log("Attempt: {$job->attempts}");

                    $start_time = microtime(true);
                    $jobInstance->handle($job->data);
                    $execution_time = number_format(microtime(true) - $start_time, 2);

                    // Mark as completed
                    $this->redisqueue->markJobAsCompleted($job, $queue);

                    $this->console_log("✓ Job completed successfully (took {$execution_time}s)\n", 'success');

                } catch (Exception $e) {
                    $this->console_log("✗ Job failed: " . $e->getMessage(), 'error');

                    // Log the error
                    log_message('error', 'Job failed: ' . $e->getMessage());

                    if ($job->attempts >= 3) {
                        $this->console_log("× Maximum attempts reached, moving to failed jobs", 'error');
                        $this->redisqueue->markJobAsFailed($job, $queue);
                    } else {
                        $delay = 60 * $job->attempts; // Progressive delay
                        $this->console_log("↻ Retrying job in {$delay} seconds", 'warning');

                        // Remove from processing queue first
                        $this->redisqueue->markJobAsCompleted($job, $queue);

                        // Then add to delayed queue for retry
                        $this->redisqueue->pushWithDelay(
                            $job->job,
                            $job->data,
                            $delay,
                            $queue
                        );
                    }
                    $this->console_log(""); // Empty line
                }
            } else {
                // Show waiting message every 30 seconds
                static $last_wait_message = 0;
                if (time() - $last_wait_message >= 30) {
                    $this->console_log("Waiting for jobs...");
                    $last_wait_message = time();
                }
            }

            // Sleep for a few seconds before next check
            usleep(1000000); // Sleep for 1 second
        }
    }

    protected function console_log($message, $type = 'info') {
        $date = date('Y-m-d H:i:s');

        // ANSI color codes for different message types
        $colors = [
            'info' => "\033[0m",     // Default
            'success' => "\033[32m",  // Green
            'error' => "\033[31m",    // Red
            'warning' => "\033[33m"   // Yellow
        ];

        // Default to info if type not found
        $color = isset($colors[$type]) ? $colors[$type] : $colors['info'];
        $reset = "\033[0m";

        // Format the message
        $formatted_message = "{$color}[{$date}] {$message}{$reset}";

        // Output to console
        fwrite(STDOUT, $formatted_message . PHP_EOL);
    }
}