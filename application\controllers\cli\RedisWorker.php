<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RedisWorker extends CI_Controller {
    private $current_job = null;
    private $current_queue = null;

    public function __construct() {
        parent::__construct();

        // Make sure this can only be run from CLI
        if (!is_cli()) {
            exit("This script can only be accessed via CLI");
        }

        // Disable output buffering
        if (ob_get_level()) ob_end_clean();

        // Turn off output buffering
        ob_implicit_flush(true);

        // Set up comprehensive error handling
        $this->setupErrorHandling();
    }

    /**
     * Set up comprehensive error handling for the worker
     */
    private function setupErrorHandling() {
        // Handle fatal errors, parse errors, etc.
        register_shutdown_function([$this, 'handleShutdown']);

        // Handle non-fatal errors
        set_error_handler([$this, 'handleError']);

        // Handle uncaught exceptions
        set_exception_handler([$this, 'handleUncaughtException']);

        // Increase memory limit for large jobs
        ini_set('memory_limit', '512M');

        // Set maximum execution time (0 = no limit for CLI)
        set_time_limit(0);
    }

    public function index($queue = 'default') {
        $this->load->library('RedisQueue');

        // Clear the screen first (works in both Windows and Unix)
        system('clear');
        system('cls');

        $this->output->set_header('Content-Type: text/plain');

        // Print worker start message
        $this->console_log("Starting worker for queue: {$queue}");
        $this->console_log("Press Ctrl+C to stop the worker\n");

        $this->current_queue = $queue;

        while (true) {
            $job = $this->redisqueue->getNextJob($queue);

            if ($job) {
                $this->current_job = $job;
                $this->processJob($job, $queue);
                $this->current_job = null;
            } else {
                // Show waiting message every 30 seconds
                static $last_wait_message = 0;
                if (time() - $last_wait_message >= 30) {
                    $this->console_log("Waiting for jobs...");
                    $last_wait_message = time();
                }
            }

            // Sleep for a few seconds before next check
            usleep(1000000); // Sleep for 1 second
        }
    }

    /**
     * Process a single job with comprehensive error handling
     */
    private function processJob($job, $queue) {
        try {
            $this->console_log("Processing job: {$job->job}");
            $this->console_log("Job ID: {$job->id}");
            $this->console_log("Attempt: {$job->attempts}");

            // Validate job file exists
            $job_file = APPPATH . 'jobs/' . $job->job . '.php';
            if (!file_exists($job_file)) {
                throw new Exception("Job file not found: {$job_file}");
            }

            // Load and execute the job
            require_once $job_file;

            if (!class_exists($job->job)) {
                throw new Exception("Job class '{$job->job}' not found");
            }

            $jobInstance = new $job->job();

            if (!method_exists($jobInstance, 'handle')) {
                throw new Exception("Job class '{$job->job}' does not have a handle method");
            }

            $start_time = microtime(true);
            $jobInstance->handle($job->data);
            $execution_time = number_format(microtime(true) - $start_time, 2);

            // Mark as completed
            $this->redisqueue->markJobAsCompleted($job, $queue);
            $this->console_log("✓ Job completed successfully (took {$execution_time}s)\n", 'success');

        } catch (Exception $e) {
            $this->handleJobFailure($job, $queue, $e);
        } catch (Error $e) {
            // Handle PHP 7+ Error objects (fatal errors, type errors, etc.)
            $this->handleJobFailure($job, $queue, $e);
        } catch (Throwable $e) {
            // Handle any other throwable (PHP 7+)
            $this->handleJobFailure($job, $queue, $e);
        }
    }

    /**
     * Handle job failure with proper retry logic
     */
    private function handleJobFailure($job, $queue, $error) {
        $error_message = $error->getMessage();
        $error_file = $error->getFile();
        $error_line = $error->getLine();

        $this->console_log("✗ Job failed: {$error_message}", 'error');
        $this->console_log("  File: {$error_file}:{$error_line}", 'error');

        // Log detailed error information
        $detailed_error = sprintf(
            "Job failed - ID: %s, Class: %s, Attempt: %d, Error: %s in %s:%d\nStack trace:\n%s",
            $job->id,
            $job->job,
            $job->attempts,
            $error_message,
            $error_file,
            $error_line,
            $error->getTraceAsString()
        );
        log_message('error', $detailed_error);

        // Determine if we should retry or fail the job
        $max_attempts = 1; // You can make this configurable

        if ($job->attempts >= $max_attempts) {
            $this->console_log("× Maximum attempts ({$max_attempts}) reached, moving to failed jobs", 'error');

            // Prepare error details for database storage
            $error_details = [
                'exception' => get_class($error) . ': ' . $error_message,
                'message' => $error_message,
                'file' => $error_file,
                'line' => $error_line,
                'trace' => $error->getTraceAsString()
            ];

            $this->redisqueue->markJobAsFailed($job, $queue, $error_details);
        } else {
            $delay = 60 * $job->attempts; // Progressive delay
            $this->console_log("↻ Retrying job in {$delay} seconds (attempt {$job->attempts}/{$max_attempts})", 'warning');

            // Remove from processing queue first
            $this->redisqueue->markJobAsCompleted($job, $queue);

            // Then add to delayed queue for retry
            $this->redisqueue->pushWithDelay(
                $job->job,
                $job->data,
                $delay,
                $queue
            );
        }

        $this->console_log(""); // Empty line
    }

    /**
     * Handle fatal errors and other shutdown errors
     */
    public function handleShutdown() {
        $error = error_get_last();

        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $error_message = sprintf(
                "Fatal error: %s in %s:%d",
                $error['message'],
                $error['file'],
                $error['line']
            );

            $this->console_log("💀 Fatal error occurred: {$error_message}", 'error');
            log_message('error', "Worker fatal error: {$error_message}");

            // If we have a current job, mark it as failed
            if ($this->current_job && $this->current_queue) {
                try {
                    $this->load->library('RedisQueue');

                    // Prepare error details for database storage
                    $error_details = [
                        'exception' => 'Fatal Error: ' . $error['message'],
                        'message' => $error['message'],
                        'file' => $error['file'],
                        'line' => $error['line'],
                        'trace' => 'Fatal error - no stack trace available'
                    ];

                    $this->redisqueue->markJobAsFailed($this->current_job, $this->current_queue, $error_details);
                    $this->console_log("Job marked as failed due to fatal error", 'error');
                } catch (Exception $e) {
                    $this->console_log("Failed to mark job as failed: " . $e->getMessage(), 'error');
                }
            }
        }
    }

    /**
     * Handle non-fatal errors
     */
    public function handleError($severity, $message, $file, $line) {
        // Don't handle suppressed errors (with @)
        if (!(error_reporting() & $severity)) {
            return false;
        }

        $error_message = sprintf(
            "Error [%s]: %s in %s:%d",
            $this->getErrorTypeName($severity),
            $message,
            $file,
            $line
        );

        $this->console_log("⚠️  {$error_message}", 'warning');
        log_message('error', "Worker error: {$error_message}");

        // For fatal-level errors, throw an exception to trigger job failure
        if (in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        }

        return true; // Don't execute PHP's internal error handler
    }

    /**
     * Handle uncaught exceptions
     */
    public function handleUncaughtException($exception) {
        $error_message = sprintf(
            "Uncaught exception: %s in %s:%d",
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        );

        $this->console_log("💥 {$error_message}", 'error');
        log_message('error', "Worker uncaught exception: {$error_message}\nStack trace:\n" . $exception->getTraceAsString());

        // If we have a current job, mark it as failed
        if ($this->current_job && $this->current_queue) {
            try {
                $this->load->library('RedisQueue');

                // Prepare error details for database storage
                $error_details = [
                    'exception' => get_class($exception) . ': ' . $exception->getMessage(),
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString()
                ];

                $this->redisqueue->markJobAsFailed($this->current_job, $this->current_queue, $error_details);
                $this->console_log("Job marked as failed due to uncaught exception", 'error');
            } catch (Exception $e) {
                $this->console_log("Failed to mark job as failed: " . $e->getMessage(), 'error');
            }
        }
    }

    /**
     * Get human-readable error type name
     */
    private function getErrorTypeName($type) {
        $error_types = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Standards',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];

        return isset($error_types[$type]) ? $error_types[$type] : 'Unknown Error';
    }

    protected function console_log($message, $type = 'info') {
        $date = date('Y-m-d H:i:s');

        // ANSI color codes for different message types
        $colors = [
            'info' => "\033[0m",     // Default
            'success' => "\033[32m",  // Green
            'error' => "\033[31m",    // Red
            'warning' => "\033[33m"   // Yellow
        ];

        // Default to info if type not found
        $color = isset($colors[$type]) ? $colors[$type] : $colors['info'];
        $reset = "\033[0m";

        // Format the message
        $formatted_message = "{$color}[{$date}] {$message}{$reset}";

        // Output to console
        fwrite(STDOUT, $formatted_message . PHP_EOL);
    }
}