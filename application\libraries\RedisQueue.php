<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RedisQueue {
    protected $CI;
    protected $redis;
    protected $default_queue = 'default';

    public function __construct() {
        $this->CI =& get_instance();

        // Load config
        $this->CI->config->load('redis', TRUE);
        $config = $this->CI->config->item('redis')['redis'];
        // Connect to Redis
        $this->redis = new Redis();
        $this->redis->connect(
            $config['host'],
            $config['port']
        );

        if (!empty($config['password'])) {
            $this->redis->auth($config['password']);
        }

        if (isset($config['database'])) {
            $this->redis->select($config['database']);
        }
    }

    public function push($job, $data = [], $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $payload = json_encode([
            'id' => uniqid('job_'),
            'job' => $job,
            'data' => $data,
            'attempts' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Add to queue
        return $this->redis->lPush("queues:{$queue}", $payload);
    }

    public function pushWithDelay($job, $data = [], $delay = 60, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $payload = json_encode([
            'id' => uniqid('job_'),
            'job' => $job,
            'data' => $data,
            'attempts' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Add to delayed queue with score as timestamp
        $executeAt = time() + $delay;
        return $this->redis->zAdd(
            "delayed:{$queue}",
            $executeAt,
            $payload
        );
    }

    public function getNextJob($queue = null) {
        $queue = $queue ?: $this->default_queue;

        // First check delayed queue
        $this->moveDelayedJobs($queue);

        // Get job from queue
        $payload = $this->redis->rPop("queues:{$queue}");
        if (!$payload) {
            return null;
        }

        $job = json_decode($payload);
        $job->attempts++;

        // Add to processing set
        $this->redis->hSet(
            "processing:{$queue}",
            $job->id,
            json_encode($job)
        );

        return $job;
    }

    protected function moveDelayedJobs($queue) {
        $now = time();

        // Get all delayed jobs that should run now
        $jobs = $this->redis->zRangeByScore(
            "delayed:{$queue}",
            '-inf',
            $now
        );

        foreach ($jobs as $job) {
            // Move from delayed to main queue
            $this->redis->lPush("queues:{$queue}", $job);

            // Remove from delayed set
            $this->redis->zRem("delayed:{$queue}", $job);
        }
    }

    public function markJobAsCompleted($job, $queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hDel("processing:{$queue}", $job->id);
    }

    public function markJobAsFailed($job, $queue = null, $error_details = null) {
        $queue = $queue ?: $this->default_queue;

        // Remove from processing
        $this->redis->hDel("processing:{$queue}", $job->id);

        // Add to failed set in Redis
        $redis_result = $this->redis->hSet(
            "failed:{$queue}",
            $job->id,
            json_encode($job)
        );

        // Also save to database for persistent storage
        $this->saveFailedJobToDatabase($job, $queue, $error_details);

        return $redis_result;
    }

    public function retryFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $job = $this->redis->hGet("failed:{$queue}", $jobId);

        if ($job) {
            // Remove from failed set
            $this->redis->hDel("failed:{$queue}", $jobId);

            // Add back to queue
            return $this->redis->lPush("queues:{$queue}", $job);
        }

        return false;
    }

    /**
     * Get all failed jobs for a queue
     */
    public function getFailedJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;

        $failedJobs = $this->redis->hGetAll("failed:{$queue}");
        $jobs = [];

        foreach ($failedJobs as $jobId => $jobData) {
            $job = json_decode($jobData, true);
            $job['id'] = $jobId;
            $jobs[] = $job;
        }

        return $jobs;
    }

    /**
     * Get failed jobs count
     */
    public function getFailedJobsCount($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hLen("failed:{$queue}");
    }

    /**
     * Clear all failed jobs
     */
    public function clearFailedJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->del("failed:{$queue}");
    }

    /**
     * Get specific failed job by ID
     */
    public function getFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $jobData = $this->redis->hGet("failed:{$queue}", $jobId);
        if ($jobData) {
            $job = json_decode($jobData, true);
            $job['id'] = $jobId;
            return $job;
        }

        return null;
    }

    /**
     * Delete specific failed job
     */
    public function deleteFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hDel("failed:{$queue}", $jobId);
    }

    /**
     * Get queue statistics
     */
    public function getQueueStats($queue = null) {
        $queue = $queue ?: $this->default_queue;

        return [
            'pending' => $this->redis->lLen("queues:{$queue}"),
            'processing' => $this->redis->hLen("processing:{$queue}"),
            'failed' => $this->redis->hLen("failed:{$queue}"),
            'delayed' => $this->redis->zCard("delayed:{$queue}")
        ];
    }

    /**
     * Get all processing jobs
     */
    public function getProcessingJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;

        $processingJobs = $this->redis->hGetAll("processing:{$queue}");
        $jobs = [];

        foreach ($processingJobs as $jobId => $jobData) {
            $job = json_decode($jobData, true);
            $job['id'] = $jobId;
            $jobs[] = $job;
        }

        return $jobs;
    }

    /**
     * Clean up stuck processing jobs (jobs that have been processing for too long)
     * @param int $timeout_minutes Jobs processing longer than this will be considered stuck
     */
    public function cleanupStuckJobs($timeout_minutes = 30, $queue = null) {
        $queue = $queue ?: $this->default_queue;
        $timeout_seconds = $timeout_minutes * 60;
        $current_time = time();

        $processingJobs = $this->getProcessingJobs($queue);
        $cleaned = 0;

        foreach ($processingJobs as $job) {
            // Check if job has been processing for too long
            $job_start_time = strtotime($job['created_at']);
            if (($current_time - $job_start_time) > $timeout_seconds) {
                // Move stuck job to failed queue
                $this->markJobAsFailed((object)$job, $queue);
                $cleaned++;
            }
        }

        return $cleaned;
    }

    /**
     * Force move a processing job to failed queue
     */
    public function forceFailProcessingJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $jobData = $this->redis->hGet("processing:{$queue}", $jobId);
        if ($jobData) {
            $job = json_decode($jobData);
            return $this->markJobAsFailed($job, $queue);
        }

        return false;
    }

    /**
     * Clear all processing jobs (use with caution!)
     */
    public function clearProcessingJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->del("processing:{$queue}");
    }

    /**
     * Clear all pending jobs from a queue (use with caution!)
     */
    public function clearPendingJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->del("queues:{$queue}");
    }

    /**
     * Save failed job to database for persistent storage
     */
    private function saveFailedJobToDatabase($job, $queue, $error_details = null) {
        try {
            // Get CodeIgniter instance
            $CI =& get_instance();

            // Load the failed job model
            $CI->load->model('Failed_job_model');

            // Prepare job data for database storage
            $job_data = [
                'connection' => 'redis',
                'queue' => $queue,
                'payload' => [
                    'id' => $job->id,
                    'job' => $job->job,
                    'data' => $job->data,
                    'attempts' => $job->attempts,
                    'created_at' => $job->created_at
                ],
                'job_class' => $job->job,
                'job_data' => $job->data,
                'attempts' => $job->attempts,
                'worker_id' => gethostname() . '_' . getmypid()
            ];

            // Add error details if provided
            if ($error_details) {
                $job_data['exception'] = $error_details['exception'] ?? 'Unknown error';
                $job_data['error_message'] = $error_details['message'] ?? null;
                $job_data['error_file'] = $error_details['file'] ?? null;
                $job_data['error_line'] = $error_details['line'] ?? null;
                $job_data['stack_trace'] = $error_details['trace'] ?? null;
            } else {
                $job_data['exception'] = 'Job failed without specific error details';
            }

            // Save to database
            $CI->Failed_job_model->save_failed_job($job_data);

        } catch (Exception $e) {
            // Log error but don't fail the main process
            log_message('error', 'Failed to save failed job to database: ' . $e->getMessage());
        }
    }

    /**
     * Get failed jobs from database
     */
    public function getFailedJobsFromDatabase($limit = 50, $offset = 0, $filters = []) {
        try {
            $CI =& get_instance();
            $CI->load->model('Failed_job_model');

            return $CI->Failed_job_model->get_failed_jobs($limit, $offset, $filters);
        } catch (Exception $e) {
            log_message('error', 'Failed to get failed jobs from database: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get failed jobs statistics from database
     */
    public function getFailedJobsStatistics($days = 7) {
        try {
            $CI =& get_instance();
            $CI->load->model('Failed_job_model');

            return $CI->Failed_job_model->get_statistics($days);
        } catch (Exception $e) {
            log_message('error', 'Failed to get failed jobs statistics: ' . $e->getMessage());
            return [];
        }
    }
}