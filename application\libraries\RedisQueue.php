<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RedisQueue {
    protected $CI;
    protected $redis;
    protected $default_queue = 'default';

    public function __construct() {
        $this->CI =& get_instance();

        // Load config
        $this->CI->config->load('redis', TRUE);
        $config = $this->CI->config->item('redis')['redis'];
        // Connect to Redis
        $this->redis = new Redis();
        $this->redis->connect(
            $config['host'],
            $config['port']
        );

        if (!empty($config['password'])) {
            $this->redis->auth($config['password']);
        }

        if (isset($config['database'])) {
            $this->redis->select($config['database']);
        }
    }

    public function push($job, $data = [], $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $payload = json_encode([
            'id' => uniqid('job_'),
            'job' => $job,
            'data' => $data,
            'attempts' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Add to queue
        return $this->redis->lPush("queues:{$queue}", $payload);
    }

    public function pushWithDelay($job, $data = [], $delay = 60, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $payload = json_encode([
            'id' => uniqid('job_'),
            'job' => $job,
            'data' => $data,
            'attempts' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Add to delayed queue with score as timestamp
        $executeAt = time() + $delay;
        return $this->redis->zAdd(
            "delayed:{$queue}",
            $executeAt,
            $payload
        );
    }

    public function getNextJob($queue = null) {
        $queue = $queue ?: $this->default_queue;

        // First check delayed queue
        $this->moveDelayedJobs($queue);

        // Get job from queue
        $payload = $this->redis->rPop("queues:{$queue}");
        if (!$payload) {
            return null;
        }

        $job = json_decode($payload);
        $job->attempts++;

        // Add to processing set
        $this->redis->hSet(
            "processing:{$queue}",
            $job->id,
            json_encode($job)
        );

        return $job;
    }

    protected function moveDelayedJobs($queue) {
        $now = time();

        // Get all delayed jobs that should run now
        $jobs = $this->redis->zRangeByScore(
            "delayed:{$queue}",
            '-inf',
            $now
        );

        foreach ($jobs as $job) {
            // Move from delayed to main queue
            $this->redis->lPush("queues:{$queue}", $job);

            // Remove from delayed set
            $this->redis->zRem("delayed:{$queue}", $job);
        }
    }

    public function markJobAsCompleted($job, $queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hDel("processing:{$queue}", $job->id);
    }

    public function markJobAsFailed($job, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        // Remove from processing
        $this->redis->hDel("processing:{$queue}", $job->id);

        // Add to failed set
        return $this->redis->hSet(
            "failed:{$queue}",
            $job->id,
            json_encode($job)
        );
    }

    public function retryFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $job = $this->redis->hGet("failed:{$queue}", $jobId);

        if ($job) {
            // Remove from failed set
            $this->redis->hDel("failed:{$queue}", $jobId);

            // Add back to queue
            return $this->redis->lPush("queues:{$queue}", $job);
        }

        return false;
    }

    /**
     * Get all failed jobs for a queue
     */
    public function getFailedJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;

        $failedJobs = $this->redis->hGetAll("failed:{$queue}");
        $jobs = [];

        foreach ($failedJobs as $jobId => $jobData) {
            $job = json_decode($jobData, true);
            $job['id'] = $jobId;
            $jobs[] = $job;
        }

        return $jobs;
    }

    /**
     * Get failed jobs count
     */
    public function getFailedJobsCount($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hLen("failed:{$queue}");
    }

    /**
     * Clear all failed jobs
     */
    public function clearFailedJobs($queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->del("failed:{$queue}");
    }

    /**
     * Get specific failed job by ID
     */
    public function getFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;

        $jobData = $this->redis->hGet("failed:{$queue}", $jobId);
        if ($jobData) {
            $job = json_decode($jobData, true);
            $job['id'] = $jobId;
            return $job;
        }

        return null;
    }

    /**
     * Delete specific failed job
     */
    public function deleteFailedJob($jobId, $queue = null) {
        $queue = $queue ?: $this->default_queue;
        return $this->redis->hDel("failed:{$queue}", $jobId);
    }

    /**
     * Get queue statistics
     */
    public function getQueueStats($queue = null) {
        $queue = $queue ?: $this->default_queue;

        return [
            'pending' => $this->redis->lLen("queues:{$queue}"),
            'processing' => $this->redis->hLen("processing:{$queue}"),
            'failed' => $this->redis->hLen("failed:{$queue}"),
            'delayed' => $this->redis->zCard("delayed:{$queue}")
        ];
    }
}