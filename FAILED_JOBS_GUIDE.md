# Failed Jobs Management Guide

This guide explains how to manage failed jobs in the SMS Portal system using both command line and web interface.

## Overview

The system has two types of failed job handling:

1. **Redis Queue Failed Jobs** - Modern queue system using Redis
2. **Database Failed Messages** - Legacy system storing failed messages in database

## Command Line Interface (CLI)

### Basic Usage

Navigate to your project directory and use the following commands:

```bash
cd c:\laragon\www\smsportal
php index.php cli/FailedJobs [command] [parameters]
```

### Available Commands

#### 1. List Failed Jobs
```bash
# List all failed jobs in default queue
php index.php cli/FailedJobs list

# List failed jobs in specific queue
php index.php cli/FailedJobs list sms
php index.php cli/FailedJobs list email
```

#### 2. Show Queue Statistics
```bash
# Show statistics for all queues
php index.php cli/FailedJobs stats

# Show statistics for specific queue
php index.php cli/FailedJobs stats default
php index.php cli/FailedJobs stats sms
```

#### 3. View Job Details
```bash
# Show detailed information about a specific failed job
php index.php cli/FailedJobs show job_12345
php index.php cli/FailedJobs show job_12345 sms
```

#### 4. Retry Failed Jobs
```bash
# Retry a specific failed job
php index.php cli/FailedJobs retry job_12345
php index.php cli/FailedJobs retry job_12345 sms

# Retry all failed jobs in a queue
php index.php cli/FailedJobs retry_all
php index.php cli/FailedJobs retry_all sms
```

#### 5. Delete Failed Jobs
```bash
# Delete a specific failed job
php index.php cli/FailedJobs delete job_12345
php index.php cli/FailedJobs delete job_12345 sms

# Clear all failed jobs from a queue (with confirmation)
php index.php cli/FailedJobs clear
php index.php cli/FailedJobs clear sms
```

#### 6. Check Database Failed Messages
```bash
# Check failed messages from database (legacy system)
php index.php cli/FailedJobs check_db_failed
```

#### 7. Manage Processing Jobs (Stuck Jobs)
```bash
# List jobs currently being processed
php index.php cli/FailedJobs processing
php index.php cli/FailedJobs processing sms

# Clean up stuck processing jobs (jobs running longer than 30 minutes)
php index.php cli/FailedJobs cleanup_stuck
php index.php cli/FailedJobs cleanup_stuck 60 sms

# Force move a specific processing job to failed queue
php index.php cli/FailedJobs force_fail job_12345
php index.php cli/FailedJobs force_fail job_12345 sms

# Clear all processing jobs (DANGEROUS - only use when no workers are running)
php index.php cli/FailedJobs clear_processing
php index.php cli/FailedJobs clear_processing sms
```

#### 8. Automated Queue Maintenance
```bash
# Run automatic cleanup of stuck jobs
php index.php cli/QueueMaintenance auto_cleanup

# Health check for all queues
php index.php cli/QueueMaintenance health_check

# Clean up old failed jobs (older than 7 days)
php index.php cli/QueueMaintenance cleanup_old_failed 7

# Emergency cleanup (move all processing jobs to failed)
php index.php cli/QueueMaintenance emergency_cleanup
```

### Example Output

```bash
$ php index.php cli/FailedJobs list

=== Failed Jobs in Queue: default ===
Found 3 failed jobs:

Job ID: job_64f8a1b2c3d4e
  Job Class: SmsProcessing
  Attempts: 3
  Created: 2024-01-15 10:30:45
  Data: {"message_id":123,"user_id":456}
  --------------------------------------------------

Job ID: job_64f8a1b2c3d4f
  Job Class: EmailNotification
  Attempts: 2
  Created: 2024-01-15 11:15:22
  Data: {"email":"<EMAIL>","template":"welcome"}
  --------------------------------------------------
```

## Web Interface

### Accessing the Dashboard

1. Login to admin panel
2. Navigate to: `http://your-domain.com/admin/FailedJobs`

### Features

- **Queue Statistics Dashboard** - Visual overview of all queue statistics
- **Failed Jobs Table** - Sortable, searchable table of failed jobs
- **Queue Selector** - Switch between different queues
- **Bulk Actions** - Retry all or clear all failed jobs
- **Individual Actions** - View, retry, or delete specific jobs
- **Export to CSV** - Download failed jobs data

### Dashboard Actions

1. **View Job Details** - Click the eye icon to see full job information
2. **Retry Job** - Click the refresh icon to retry a specific job
3. **Delete Job** - Click the trash icon to permanently delete a job
4. **Retry All** - Retry all failed jobs in the selected queue
5. **Clear Queue** - Delete all failed jobs in the selected queue
6. **Export CSV** - Download failed jobs data as CSV file

## Redis Queue Structure

The system uses the following Redis keys:

- `queues:{queue}` - Pending jobs
- `processing:{queue}` - Currently processing jobs
- `failed:{queue}` - Failed jobs
- `delayed:{queue}` - Delayed jobs

## Common Scenarios

### 1. Check for Failed Jobs
```bash
# Quick check of all queue statistics
php index.php cli/FailedJobs stats
```

### 2. Investigate a Failed Job
```bash
# List failed jobs to find the job ID
php index.php cli/FailedJobs list

# Get detailed information
php index.php cli/FailedJobs show job_12345
```

### 3. Retry Failed Jobs
```bash
# Retry a specific job after fixing the issue
php index.php cli/FailedJobs retry job_12345

# Retry all failed jobs after system maintenance
php index.php cli/FailedJobs retry_all
```

### 4. Clean Up Old Failed Jobs
```bash
# Delete specific jobs that can't be fixed
php index.php cli/FailedJobs delete job_12345

# Clear all failed jobs after investigation
php index.php cli/FailedJobs clear
```

### 5. Monitor Database Failed Messages
```bash
# Check legacy failed messages
php index.php cli/FailedJobs check_db_failed
```

## Automation with Cron Jobs

You can automate failed job management with cron jobs:

```bash
# Automatic queue maintenance every 15 minutes (recommended)
*/15 * * * * cd /path/to/smsportal && php index.php cli/QueueMaintenance auto_cleanup >> /var/log/queue_maintenance.log 2>&1

# Health check every hour
0 * * * * cd /path/to/smsportal && php index.php cli/QueueMaintenance health_check >> /var/log/queue_health.log 2>&1

# Clean up old failed jobs weekly (older than 7 days)
0 3 * * 0 cd /path/to/smsportal && php index.php cli/QueueMaintenance cleanup_old_failed 7 >> /var/log/queue_cleanup.log 2>&1

# Manual options (use with caution):
# Check for failed jobs every hour
0 * * * * cd /path/to/smsportal && php index.php cli/FailedJobs stats >> /var/log/failed_jobs.log

# Auto-retry failed jobs daily at 2 AM (only if you're confident about root causes)
# 0 2 * * * cd /path/to/smsportal && php index.php cli/FailedJobs retry_all
```

### Recommended Cron Setup

Add this to your crontab (`crontab -e`):

```bash
# Queue maintenance - clean stuck jobs every 15 minutes
*/15 * * * * cd /path/to/smsportal && php index.php cli/QueueMaintenance auto_cleanup >> /var/log/queue_maintenance.log 2>&1

# Health monitoring - check queue health every hour
0 * * * * cd /path/to/smsportal && php index.php cli/QueueMaintenance health_check >> /var/log/queue_health.log 2>&1

# Cleanup old failed jobs weekly
0 3 * * 0 cd /path/to/smsportal && php index.php cli/QueueMaintenance cleanup_old_failed 7 >> /var/log/queue_cleanup.log 2>&1
```

## Troubleshooting

### Common Issues

1. **Jobs Stuck in Processing Queue**
   - **Symptoms**: Jobs remain in processing queue and never complete or fail
   - **Causes**: Worker crashed, server restart, or job hanging
   - **Solutions**:
     ```bash
     # Check what's currently processing
     php index.php cli/FailedJobs processing

     # Clean up stuck jobs (move to failed after 30 minutes)
     php index.php cli/FailedJobs cleanup_stuck

     # Force specific job to failed
     php index.php cli/FailedJobs force_fail job_12345

     # Emergency: clear all processing (when no workers running)
     php index.php cli/FailedJobs clear_processing
     ```

2. **High Number of Failed Jobs**
   - **Check root cause**: Look at error messages in failed jobs
   - **Common causes**: Database connection issues, insufficient balance, API failures
   - **Solutions**:
     ```bash
     # Investigate failed jobs
     php index.php cli/FailedJobs list
     php index.php cli/FailedJobs show job_12345

     # After fixing root cause, retry jobs
     php index.php cli/FailedJobs retry_all
     ```

3. **Redis Connection Error**
   - Check Redis server is running
   - Verify Redis configuration in `application/config/redis.php`

4. **Permission Denied**
   - Ensure proper file permissions
   - Run commands with appropriate user privileges

5. **Job Not Found**
   - Job may have been already processed or deleted
   - Check if you're using the correct queue name

6. **Worker Not Processing Jobs**
   - Check if RedisWorker is running: `ps aux | grep RedisWorker`
   - Restart worker: `php index.php cli/RedisWorker index`
   - Check worker logs for errors

### Getting Help

```bash
# Show help information
php index.php cli/FailedJobs
php index.php cli/FailedJobs index
```

## Integration with Existing System

The failed jobs management integrates with:

- **RedisQueue Library** - Core queue functionality
- **Admin Panel** - Web interface for administrators
- **Message Model** - Legacy database failed messages
- **Worker Process** - Automatic job failure handling

## Best Practices

1. **Regular Monitoring** - Check failed jobs daily
2. **Investigate Root Causes** - Don't just retry, fix underlying issues
3. **Clean Up Regularly** - Remove old failed jobs that can't be fixed
4. **Use Appropriate Queues** - Separate different types of jobs
5. **Monitor Statistics** - Keep track of failure rates
6. **Backup Before Clearing** - Export failed jobs before mass deletion
