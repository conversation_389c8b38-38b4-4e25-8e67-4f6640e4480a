<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-05-31 01:56:06 --> Severity: Notice --> Trying to access array offset on value of type null C:\laragon\www\smsportal\application\services\AdminNotificationService.php 91
ERROR - 2025-05-31 01:57:34 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: UPDATE `sms_processing_jobs2` SET `status` = 'failed2', `error_message` = 'Your balance is insufficient to complete the process.', `completed_at` = '2025-05-31 01:57:34'
WHERE `id` = 62
ERROR - 2025-05-31 01:57:34 --> Severity: Notice --> Trying to access array offset on value of type null C:\laragon\www\smsportal\application\services\AdminNotificationService.php 91
ERROR - 2025-05-31 02:01:01 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: SELECT *
FROM `sms_processing_jobs2`
WHERE `id` = 63
ERROR - 2025-05-31 02:01:01 --> Severity: error --> Exception: Call to a member function row() on bool C:\laragon\www\smsportal\application\jobs\SmsProcessing.php 34
ERROR - 2025-05-31 02:05:17 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: SELECT *
FROM `sms_processing_jobs2`
WHERE `id` = 64
ERROR - 2025-05-31 02:05:17 --> Severity: error --> Exception: Call to a member function row() on bool C:\laragon\www\smsportal\application\jobs\SmsProcessing.php 34
ERROR - 2025-05-31 02:08:39 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: SELECT *
FROM `sms_processing_jobs2`
WHERE `id` = 65
ERROR - 2025-05-31 02:08:39 --> Job failed - ID: job_683a3a770b95a, Class: SmsProcessing, Attempt: 1, Error: Call to a member function row() on bool in C:\laragon\www\smsportal\application\jobs\SmsProcessing.php:34
Stack trace:
#0 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(111): SmsProcessing->handle(Object(stdClass))
#1 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(66): RedisWorker->processJob(Object(stdClass), 'default')
#2 C:\laragon\www\smsportal\system\core\CodeIgniter.php(529): RedisWorker->index()
#3 C:\laragon\www\smsportal\index.php(274): require_once('C:\\laragon\\www\\...')
#4 {main}
ERROR - 2025-05-31 02:11:52 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: SELECT *
FROM `sms_processing_jobs2`
WHERE `id` = 65
ERROR - 2025-05-31 02:11:52 --> Job failed - ID: job_683a3a77de7f0, Class: SmsProcessing, Attempt: 1, Error: Call to a member function row() on bool in C:\laragon\www\smsportal\application\jobs\SmsProcessing.php:34
Stack trace:
#0 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(111): SmsProcessing->handle(Object(stdClass))
#1 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(66): RedisWorker->processJob(Object(stdClass), 'default')
#2 C:\laragon\www\smsportal\system\core\CodeIgniter.php(529): RedisWorker->index()
#3 C:\laragon\www\smsportal\index.php(274): require_once('C:\\laragon\\www\\...')
#4 {main}
ERROR - 2025-05-31 02:12:49 --> Query error: Table 'sms.sms_processing_jobs2' doesn't exist - Invalid query: SELECT *
FROM `sms_processing_jobs2`
WHERE `id` = 65
ERROR - 2025-05-31 02:12:49 --> Job failed - ID: job_683a3a77de7f0, Class: SmsProcessing, Attempt: 2, Error: Call to a member function row() on bool in C:\laragon\www\smsportal\application\jobs\SmsProcessing.php:34
Stack trace:
#0 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(111): SmsProcessing->handle(Object(stdClass))
#1 C:\laragon\www\smsportal\application\controllers\cli\RedisWorker.php(66): RedisWorker->processJob(Object(stdClass), 'default')
#2 C:\laragon\www\smsportal\system\core\CodeIgniter.php(529): RedisWorker->index()
#3 C:\laragon\www\smsportal\index.php(274): require_once('C:\\laragon\\www\\...')
#4 {main}
