<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TestWorkerErrors extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Only allow CLI access
        if (!$this->input->is_cli_request()) {
            show_error('This script can only be accessed via command line.');
        }
        
        $this->load->library('redisqueue');
    }

    /**
     * Test different error scenarios
     * Usage: php index.php cli/TestWorkerErrors test_error <error_type>
     */
    public function test_error($error_type = 'exception') {
        echo "=== Testing Worker Error Handling ===\n";
        echo "Error type: {$error_type}\n\n";
        
        // Push a test job that will cause an error
        $job_data = (object)[
            'error_type' => $error_type,
            'test_id' => uniqid('test_'),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $result = $this->redisqueue->push('TestErrorJob', $job_data);
        
        if ($result) {
            echo "✓ Test job pushed to queue successfully\n";
            echo "Now run the worker to see error handling in action:\n";
            echo "php index.php cli/RedisWorker index\n\n";
            
            echo "Available error types:\n";
            echo "  exception  - Throws a regular exception\n";
            echo "  fatal      - Causes a fatal error (method not found)\n";
            echo "  database   - Database connection/query error\n";
            echo "  file       - File not found error\n";
            echo "  warning    - PHP warning (undefined array key)\n";
            echo "  memory     - Memory exhaustion (be careful!)\n";
            echo "  timeout    - Long-running job (2 minutes)\n";
            echo "  success    - Successful job (no error)\n";
        } else {
            echo "✗ Failed to push test job to queue\n";
        }
    }

    /**
     * Test multiple error scenarios
     * Usage: php index.php cli/TestWorkerErrors test_multiple
     */
    public function test_multiple() {
        echo "=== Testing Multiple Error Scenarios ===\n";
        
        $error_types = ['success', 'exception', 'warning', 'database', 'file'];
        
        foreach ($error_types as $error_type) {
            $job_data = (object)[
                'error_type' => $error_type,
                'test_id' => uniqid('multi_test_'),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            $result = $this->redisqueue->push('TestErrorJob', $job_data);
            
            if ($result) {
                echo "✓ Pushed {$error_type} test job\n";
            } else {
                echo "✗ Failed to push {$error_type} test job\n";
            }
        }
        
        echo "\nAll test jobs pushed. Run the worker to process them:\n";
        echo "php index.php cli/RedisWorker index\n";
    }

    /**
     * Check queue status
     * Usage: php index.php cli/TestWorkerErrors status
     */
    public function status() {
        echo "=== Queue Status ===\n";
        
        $queues = ['default'];
        
        foreach ($queues as $queue) {
            $stats = $this->redisqueue->getQueueStats($queue);
            
            echo "Queue: {$queue}\n";
            echo "  Pending: {$stats['pending']}\n";
            echo "  Processing: {$stats['processing']}\n";
            echo "  Failed: {$stats['failed']}\n";
            echo "  Delayed: {$stats['delayed']}\n";
            
            if ($stats['failed'] > 0) {
                echo "\nFailed jobs:\n";
                $failed_jobs = $this->redisqueue->getFailedJobs($queue);
                foreach ($failed_jobs as $job) {
                    echo "  - Job ID: {$job['id']}, Class: {$job['job']}, Attempts: {$job['attempts']}\n";
                }
            }
            
            if ($stats['processing'] > 0) {
                echo "\nProcessing jobs:\n";
                $processing_jobs = $this->redisqueue->getProcessingJobs($queue);
                foreach ($processing_jobs as $job) {
                    $processing_time = time() - strtotime($job['created_at']);
                    echo "  - Job ID: {$job['id']}, Class: {$job['job']}, Processing for: {$processing_time}s\n";
                }
            }
            
            echo "\n";
        }
    }

    /**
     * Clean up test jobs
     * Usage: php index.php cli/TestWorkerErrors cleanup
     */
    public function cleanup() {
        echo "=== Cleaning Up Test Jobs ===\n";
        
        $queue = 'default';
        
        // Clear failed jobs
        $failed_count = $this->redisqueue->getFailedJobsCount($queue);
        if ($failed_count > 0) {
            $this->redisqueue->clearFailedJobs($queue);
            echo "✓ Cleared {$failed_count} failed jobs\n";
        }
        
        // Clear processing jobs (be careful!)
        $processing_stats = $this->redisqueue->getQueueStats($queue);
        if ($processing_stats['processing'] > 0) {
            echo "Warning: {$processing_stats['processing']} jobs are currently processing\n";
            echo "Type 'yes' to clear them anyway: ";
            
            $handle = fopen("php://stdin", "r");
            $confirmation = trim(fgets($handle));
            fclose($handle);
            
            if (strtolower($confirmation) === 'yes') {
                $this->redisqueue->clearProcessingJobs($queue);
                echo "✓ Cleared processing jobs\n";
            } else {
                echo "Processing jobs left untouched\n";
            }
        }
        
        echo "Cleanup complete\n";
    }

    /**
     * Show help
     */
    public function index() {
        echo "=== Worker Error Testing Tool ===\n\n";
        echo "Available commands:\n";
        echo "  test_error <type>  - Test specific error type\n";
        echo "  test_multiple      - Test multiple error types\n";
        echo "  status             - Show queue status\n";
        echo "  cleanup            - Clean up test jobs\n\n";
        echo "Example:\n";
        echo "  php index.php cli/TestWorkerErrors test_error exception\n";
        echo "  php index.php cli/TestWorkerErrors status\n";
    }
}
