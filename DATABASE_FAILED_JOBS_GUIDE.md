# Database Failed Jobs System

This guide explains the new database-based failed jobs system that provides persistent storage and advanced analytics for failed jobs.

## Overview

The system now stores failed jobs in both:
1. **Redis** - For fast access and immediate processing
2. **Database** - For persistent storage, analytics, and long-term tracking

## Database Schema

The `failed_jobs` table contains the following fields:

```sql
- id (bigint) - Primary key
- uuid (varchar) - Unique identifier for the job
- connection (varchar) - Connection type (default: 'redis')
- queue (varchar) - Queue name (default: 'default')
- payload (longtext) - Complete job payload as JSON
- exception (longtext) - Exception details
- failed_at (timestamp) - When the job failed
- job_class (varchar) - Job class name
- job_data (longtext) - Job data as JSON
- attempts (int) - Number of attempts made
- error_message (text) - Error message
- error_file (varchar) - File where error occurred
- error_line (int) - Line number where error occurred
- stack_trace (longtext) - Full stack trace
- worker_id (varchar) - Worker that processed the job
- created_at (timestamp) - Record creation time
- updated_at (timestamp) - Record update time
```

## Setup Instructions

### 1. Create the Database Table

**Option A: Run SQL Script**
```bash
mysql -u username -p database_name < create_failed_jobs_table.sql
```

**Option B: Use CodeIgniter Migration**
```bash
php index.php cli/DatabaseFailedJobs migrate
```

**Option C: Manual SQL**
```sql
-- Copy and run the SQL from create_failed_jobs_table.sql
```

### 2. Verify Installation

```bash
# Check if table was created successfully
php index.php cli/DatabaseFailedJobs list

# Should show "No failed jobs found in database" if empty
```

## Command Line Interface

### Basic Commands

```bash
# List failed jobs from database
php index.php cli/DatabaseFailedJobs list

# Show specific job details
php index.php cli/DatabaseFailedJobs show 123

# Get statistics for last 7 days
php index.php cli/DatabaseFailedJobs stats

# Show most common errors
php index.php cli/DatabaseFailedJobs errors
```

### Management Commands

```bash
# Delete a specific job
php index.php cli/DatabaseFailedJobs delete 123

# Clean up jobs older than 30 days
php index.php cli/DatabaseFailedJobs cleanup 30

# Clear all failed jobs (with confirmation)
php index.php cli/DatabaseFailedJobs clear_all

# Export to CSV
php index.php cli/DatabaseFailedJobs export failed_jobs.csv
```

### Advanced Usage

```bash
# List with pagination
php index.php cli/DatabaseFailedJobs list 50 100  # 50 records, offset 100

# Statistics for different periods
php index.php cli/DatabaseFailedJobs stats 30     # Last 30 days
php index.php cli/DatabaseFailedJobs stats 1      # Last 24 hours

# Show top 20 error messages
php index.php cli/DatabaseFailedJobs errors 20
```

## Web Interface

### Accessing the Dashboard

1. Login to admin panel
2. Navigate to: `http://your-domain.com/admin/FailedJobs`
3. The dashboard now shows both Redis and Database statistics

### New Features

- **Database Failed Jobs Tab** - View persistent failed jobs
- **Advanced Filtering** - Filter by queue, job class, date range
- **Error Analytics** - Common error patterns and statistics
- **Export Functionality** - Download failed jobs data
- **Detailed Job View** - Complete error information including stack traces

## Integration with Existing System

### Automatic Storage

When a job fails, the system automatically:

1. **Stores in Redis** - For immediate retry logic
2. **Stores in Database** - For persistent tracking
3. **Logs Error Details** - Complete error information
4. **Records Context** - Worker ID, timestamps, attempts

### Error Information Captured

```php
// Example of data stored for each failed job
[
    'uuid' => 'unique-job-identifier',
    'queue' => 'sms',
    'job_class' => 'SmsProcessing',
    'error_message' => 'Your balance is insufficient',
    'error_file' => '/path/to/SmsProcessing.php',
    'error_line' => 45,
    'stack_trace' => 'Full stack trace...',
    'worker_id' => 'server1_12345',
    'attempts' => 3,
    'job_data' => '{"message_id": 123, "user_id": 456}',
    'failed_at' => '2024-01-15 10:30:45'
]
```

## Analytics and Reporting

### Statistics Available

```bash
# Get comprehensive statistics
php index.php cli/DatabaseFailedJobs stats 7
```

**Output includes:**
- Total failed jobs in period
- Breakdown by queue
- Breakdown by job class
- Daily failure trends
- Most common error messages

### Common Use Cases

**1. Identify Problem Jobs**
```bash
# Find jobs that fail most often
php index.php cli/DatabaseFailedJobs stats 30
```

**2. Debug Specific Errors**
```bash
# Show detailed error information
php index.php cli/DatabaseFailedJobs show 123
```

**3. Monitor Trends**
```bash
# Export data for external analysis
php index.php cli/DatabaseFailedJobs export monthly_report.csv
```

**4. Clean Up Old Data**
```bash
# Remove jobs older than 90 days
php index.php cli/DatabaseFailedJobs cleanup 90
```

## Maintenance and Monitoring

### Automated Cleanup

Add to crontab for automatic maintenance:

```bash
# Clean up old failed jobs weekly (keep 30 days)
0 2 * * 0 cd /path/to/smsportal && php index.php cli/DatabaseFailedJobs cleanup 30

# Generate weekly reports
0 9 * * 1 cd /path/to/smsportal && php index.php cli/DatabaseFailedJobs export weekly_$(date +\%Y\%m\%d).csv
```

### Monitoring Queries

**Check recent failure rate:**
```sql
SELECT 
    DATE(failed_at) as date,
    COUNT(*) as failures
FROM failed_jobs 
WHERE failed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(failed_at)
ORDER BY date DESC;
```

**Find most problematic job classes:**
```sql
SELECT 
    job_class,
    COUNT(*) as failure_count,
    COUNT(DISTINCT error_message) as unique_errors
FROM failed_jobs 
WHERE failed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY job_class
ORDER BY failure_count DESC;
```

## Performance Considerations

### Database Optimization

1. **Indexes** - The table includes optimized indexes for common queries
2. **Cleanup** - Regular cleanup prevents table bloat
3. **Partitioning** - Consider partitioning by date for large volumes

### Storage Management

```bash
# Check table size
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_name = 'failed_jobs';
```

## Troubleshooting

### Common Issues

**1. Table Not Found**
```bash
# Create the table
php index.php cli/DatabaseFailedJobs migrate
```

**2. Permission Errors**
```bash
# Check database permissions
# Ensure user has INSERT, SELECT, UPDATE, DELETE on failed_jobs table
```

**3. Large Table Size**
```bash
# Clean up old records
php index.php cli/DatabaseFailedJobs cleanup 7
```

### Verification

**Test the system:**
```bash
# Create a test failed job
php index.php cli/TestWorkerErrors test_error exception

# Check if it appears in database
php index.php cli/DatabaseFailedJobs list
```

## Benefits

### Persistent Storage
- **Data Retention** - Failed jobs survive Redis restarts
- **Historical Analysis** - Track failure patterns over time
- **Compliance** - Meet data retention requirements

### Enhanced Debugging
- **Complete Context** - Full error details and stack traces
- **Worker Tracking** - Know which worker processed the job
- **Timing Information** - Precise failure timestamps

### Better Monitoring
- **Trend Analysis** - Identify increasing failure rates
- **Error Patterns** - Group similar failures
- **Performance Metrics** - Track job processing health

### Operational Benefits
- **Backup and Recovery** - Database backups include failed job data
- **Reporting** - Generate failure reports for management
- **Alerting** - Set up alerts based on failure thresholds

## Migration from Redis-Only System

The new system is **backward compatible**. Existing Redis failed jobs continue to work, and new failures are stored in both Redis and database.

**No action required** - the system automatically starts using database storage for new failed jobs.
