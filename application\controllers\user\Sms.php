<?php

defined('BASEPATH') or exit('No direct script access allowed');
ini_set('memory_limit', '-1');
use OSS\OssClient;
use OSS\Core\OssException;

class Sms extends MY_Controller
{

    var $entry_view = "user/index";
    var $message;
    var $real_time_send_limit;
    var $cron_send_limit;

    public function __construct()
    {
        parent::__construct();
        $this->load->library('curl');
        $this->load->model('setting');
        $this->load->model('outbox');
        $this->load->model('message');
        $this->load->model('messagedetails');
        $this->load->model('contact');
        $this->load->model('contactgroup');
        $this->load->model('adcontact');
        $this->load->model('country');
        $this->load->model('user');
        $this->load->model('operator');
        $this->load->model('whitelisturl');
        $this->load->model('sender');
    }

    public function info()
    {
        phpinfo();
    }

    // Private Functions



    ///////////////////////
///////////////
    private function check_message($message)
    {
        $message_words = explode(" ", $message);
        $bad_words = explode(",", $this->site_settings['bad_words']);
        foreach ($message_words as $word) {
            if (in_array($word, $bad_words)) {
                $this->db->insert(
                    'badwords_log',
                    array("user_id" => $this->session->userdata('user_logged_info')['id'], "badword" => $word)
                );

                return false;
            }
        }
        return true;
    }

    private function check_advertising_words_message($message)
    {
        $message_words = explode(" ", $message);
        $advertising_words = explode(",", $this->site_settings['advertising_words']);
        foreach ($message_words as $word) {
            if (in_array($word, $advertising_words)) {
                return true;
            }
        }
        return false;
    }

    private function check_url_in_message($message)
    {

        $message_words = preg_split('/\s+/', $message);
        foreach ($message_words as $word) {
            $url = $this->check_url($word);
            if ($url) {
                $url = parse_url($url)['host'];
                $whitelist_url = $this->whitelisturl->get_by_url($url);
                if (!empty($whitelist_url)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    private function check_url($url)
    {
        if (filter_var($url, FILTER_VALIDATE_URL) === false) {
            if (filter_var("http://$url", FILTER_VALIDATE_URL) === false) {
                return false;
            }
            $url = "http://$url";
        }

        $host = parse_url($url, PHP_URL_HOST);
        if (!preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $host)) {
            return false;
        }

        return $url;
    }

    private function check_sender_count_msg($count_msg)
    {
        $variable_sms_send_without_warning = $this->site_settings['variable_sms_send_without_warning'];
        if ($count_msg > $variable_sms_send_without_warning) {
            return true;
        }
        return false;
    }

    // Read

    public function index($message_id = 0)
    {
        if ($this->session->userdata('user_logged_in')) {
            if (!$this->site_settings['site_sending_case']) {
                $this->page_data['view'] = 'user/view_sending_out';
                $this->load_view();
            } else {
                $this->load->model('user');
                $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
                /*  ///////البترول الذهبي
                  if($this->session->userdata('user_logged_info')['id']==721){
                      dd('not authorized');

                  }*/
                $this->page_data['view'] = 'user/index_sms';
                $this->page_data['inner_page_data']['favorit_messages'] = $user->get_favorite_messages();
                $this->page_data['inner_page_data']['type'] = 'NORMAL';
                $this->load->model('favorite');
                $favorit = $this->favorite->get_by_id($message_id);
                if (!empty($favorit)) {
                    $this->page_data['inner_page_data']['message_text'] = $favorit->text;
                }
                $this->page_data['inner_page_data']['unclassified_cnt'] = $user->get_unclassified_contacts_cnt();
                // echo json_encode( $user->get_contact_groups());
                // die();
                $this->page_data['inner_page_data']['contact_groups'] = $user->get_contact_groups();
                $this->page_data['inner_page_data']['granted_contact_groups'] = $user->get_granted_contact_groups();
                if (count($user->get_senders()) > 0 || $user->get_granted_senders() > 0) {
                    $this->page_data['inner_page_data']['senders'] = $user->get_active_senders();
                    $this->page_data['inner_page_data']['use_senders'] = 1;
                } else {
                    $this->page_data['inner_page_data']['use_senders'] = 0;
                    $this->page_data['inner_page_data']['senders'] = [
                        [
                            'id' => 0,
                            'name' => 'Dreams',
                            'status' => 1,
                            'default' => 1,

                        ]
                    ];
                }
                $this->page_data['inner_page_data']['granted_senders'] = $user->get_granted_senders();
                if ($this->site_settings['enable_active_sender_number']) {
                    array_push($this->page_data['inner_page_data']['senders'], array("name" => $user->number, "default" => 0));
                }
                $this->message = [];
                $this->load_view();
            }
        } else {
            redirect('');
        }
    }
    public function index_test($message_id = 0)
    {
        if ($this->session->userdata('user_logged_in')) {
            if (!$this->site_settings['site_sending_case']) {
                $this->page_data['view'] = 'user/view_sending_out';
                $this->load_view();
            } else {
                $this->load->model('user');
                $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
                $this->page_data['view'] = 'user/index_sms_test';
                $this->page_data['inner_page_data']['favorit_messages'] = $user->get_favorite_messages();
                $this->page_data['inner_page_data']['type'] = 'NORMAL';
                $this->load->model('favorite');
                $favorit = $this->favorite->get_by_id($message_id);
                if (!empty($favorit)) {
                    $this->page_data['inner_page_data']['message_text'] = $favorit->text;
                }
                $this->page_data['inner_page_data']['unclassified_cnt'] = $user->get_unclassified_contacts_cnt();
                // echo json_encode( $user->get_contact_groups());
                // die();
                $this->page_data['inner_page_data']['contact_groups'] = $user->get_contact_groups();
                $this->page_data['inner_page_data']['granted_contact_groups'] = $user->get_granted_contact_groups();
                $this->page_data['inner_page_data']['senders'] = $user->get_active_senders();
                $this->page_data['inner_page_data']['granted_senders'] = $user->get_granted_senders();
                if ($this->site_settings['enable_active_sender_number']) {
                    array_push($this->page_data['inner_page_data']['senders'], array("name" => $user->number, "default" => 0));
                }

                $this->message = [];
                $this->load_view();
            }
        } else {
            redirect('');
        }
    }

    public function variables($message_id = 0)
    {
        if ($this->session->userdata('user_logged_in')) {
            if (!$this->site_settings['site_sending_case']) {
                $this->page_data['view'] = 'user/view_sending_out';
                $this->load_view();
            } else {
                $this->load->model('user');
                $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
                $this->page_data['view'] = 'user/index_sms_variables';
                $this->page_data['inner_page_data']['favorit_messages'] = $user->get_favorite_messages();
                $this->page_data['inner_page_data']['type'] = 'VARIABLES';
                $this->load->model('favorite');
                $favorit = $this->favorite->get_by_id($message_id);
                if (!empty($favorit)) {
                    $this->page_data['inner_page_data']['message_text'] = $favorit->text;
                }
                $this->page_data['inner_page_data']['senders'] = $user->get_active_senders();
                $this->page_data['inner_page_data']['granted_senders'] = $user->get_granted_senders();
                if ($this->site_settings['enable_active_sender_number']) {
                    array_push($this->page_data['inner_page_data']['senders'], array("name" => $user->number, "default" => 0));
                }

                $this->message = [];
                $this->load_view();

            }
        } else {
            redirect('');
        }
    }
    public function calendars($message_id = 0)
    {
        if ($this->session->userdata('user_logged_in')) {
            if (!$this->site_settings['site_sending_case']) {
                $this->page_data['view'] = 'user/view_sending_out';
                $this->load_view();
            } else {
                $this->load->model('user');
                $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);

                $this->page_data['view'] = 'user/index_sms_calendar';
                $this->page_data['inner_page_data']['favorit_messages'] = $user->get_favorite_messages();
                $this->page_data['inner_page_data']['type'] = 'CALENDAR';
                $this->load->model('favorite');
                $favorit = $this->favorite->get_by_id($message_id);
                if (!empty($favorit)) {
                    $this->page_data['inner_page_data']['message_text'] = $favorit->text;
                }
                $this->page_data['inner_page_data']['unclassified_cnt'] = $user->get_unclassified_contacts_cnt();
                // echo json_encode( $user->get_contact_groups());
                // die();
                $this->page_data['inner_page_data']['contact_groups'] = $user->get_contact_groups();
                $this->page_data['inner_page_data']['granted_contact_groups'] = $user->get_granted_contact_groups();
                if (count($user->get_senders()) > 0 || $user->get_granted_senders() > 0) {
                    $this->page_data['inner_page_data']['senders'] = $user->get_active_senders();
                    $this->page_data['inner_page_data']['use_senders'] = 1;
                } else {
                    $this->page_data['inner_page_data']['use_senders'] = 0;
                    $this->page_data['inner_page_data']['senders'] = [
                        [
                            'id' => 0,
                            'name' => 'Dreams',
                            'status' => 1,
                            'default' => 1,

                        ]
                    ];
                }
                $this->page_data['inner_page_data']['granted_senders'] = $user->get_granted_senders();
                if ($this->site_settings['enable_active_sender_number']) {
                    array_push($this->page_data['inner_page_data']['senders'], array("name" => $user->number, "default" => 0));
                }

                $this->message = [];
                $this->load_view();
            }
        } else {
            redirect('');
        }
    }
    public function ads($message_id = 0)
    {
        if ($this->session->userdata('user_logged_in')) {
            if (!$this->site_settings['site_sending_case']) {
                $this->page_data['view'] = 'user/view_sending_out';
                $this->load_view();
            } else {
                $this->load->model('user');
                $this->load->model('tag');
                $this->load->model('UserTag');
                $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
                $this->page_data['view'] = 'user/index_sms_ads';
                $this->page_data['inner_page_data']['favorit_messages'] = $user->get_favorite_messages();
                $this->page_data['inner_page_data']['type'] = 'ADS';
                $this->load->model('favorite');
                $favorit = $this->favorite->get_by_id($message_id);
                if (!empty($favorit)) {
                    $this->page_data['inner_page_data']['message_text'] = $favorit->text;
                }
                $this->page_data['inner_page_data']['senders'] = $user->get_active_senders();
                $this->page_data['inner_page_data']['granted_senders'] = $user->get_granted_senders();

                $this->page_data['inner_page_data']['tags'] = $this->UserTag->get_all_array($this->session->userdata('user_logged_info')['id']);

                if ($this->site_settings['enable_active_sender_number']) {
                    array_push($this->page_data['inner_page_data']['senders'], array("name" => $user->number, "default" => 0));
                }

                $this->message = [];
                $this->load_view();
            }
        } else {
            redirect('');
        }
    }

    public function ads_getCounts()
    {
        $this->load->model('tag');
        $parent_id = $this->input->get('parent_id');
        $response = $this->tag->ads_getCounts($parent_id);
        echo json_encode($response);
    }

    public function resend($message_id)
    {

        if ($this->session->userdata('user_logged_in')) {
            $controller_message = $this->message;
            $this->load->model('message');
            $this->load->helper('message');
            $sms = $this->message->get_by_id_user_id($message_id, $this->session->userdata('user_logged_info')['id']);
            $this->message = $controller_message;
            if (empty($sms)) {
                $this->index();
            } elseif ($sms->variables_message) {
                $this->page_data['inner_page_data']['resent_message'] = $sms->to_array();
                $this->variables();
            } else {
                $sms->text = decrept_message($sms->text, $sms->encrypted);
                $this->page_data['inner_page_data']['resent_message'] = $sms->to_array();
                $this->index();
            }
            $this->message = $controller_message;
        } else {
            redirect('');
        }
    }

    public function phonebook_autocomplete()
    {
        if ($this->session->userdata('user_logged_in')) {
            $res = array();
            $this->load->model('user');
            $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
            $contacts = $user->get_contacts_like($this->input->get('var'), 0, 10);
            $data = array();
            foreach ($contacts as $contact) {
                $text = $contact->number;
                if (!empty($contact->name)) {
                    $text .= "-" . $contact->name;
                }
                if (!empty($contact->group_name)) {
                    $text .= "-" . $contact->group_name;
                }
                if (!empty($contact->parent_group_name)) {
                    $text .= "-" . $contact->parent_group_name;
                }
                $data[] = array('value' => $contact->number, 'name' => $text);
            }
            header("Content-type: application/json");
            echo json_encode($data);
        }
    }

    public function form_sender()
    {
        if ($this->session->userdata('user_logged_in')) {
            $this->load->view(
                'user/form_sender',
                array(
                    'entry' => null,
                    'save_in_new_page' => 1,
                    'view_settings' => $this->view_settings
                )
            );
        } else {
            $this->load->view('user/form_login', array('current_path' => 'user/senders'));
        }
    }

    // Insert & Update
    function upload_excel()
    {
        if (!isset($_FILES['excel_file'])) {
            echo json_encode(array("response" => "error", "text" => $this->lang->line('msg_error_excel')));
        } else {
            $result = $this->upload_file_sso($_FILES["excel_file"], "uploads", "xlsx");

            if ($result['status'] == 1) {//
                $config = parse_ini_file('.env', true);
                try {
                    $ossClient = new OssClient($config['s3']['OSS_ACCESS_KEY_ID'], $config['s3']['OSS_ACCESS_KEY_SECRET'], $config['s3']['OSS_ENDPOINT']);
                } catch (OssException $e) {
                    print $e->getMessage();
                }
                $options = array(
                    OssClient::OSS_FILE_DOWNLOAD => './uploads/' . $result['name'],
                );
                try {
                    $content = $ossClient->getObject($config['s3']['OSS_BUCKET'], 'uploads/' . $result['name'], $options);
                } catch (OssException $e) {
                    echo json_encode(array("response" => "error", "text" => $this->lang->line('msg_error_excel_upload')));
                }

                $this->session->set_userdata('excel_file_numbers', $result['name']);
                $type = $this->input->post("sms_type");
                if ($type == "VARIABLES") {
                    $this->load->library('simplexlsx');
                    $xlsx = new SimpleXLSX('./uploads/' . $this->session->userdata('excel_file_numbers'));
                    $rows = array();
                    if ($xlsx->sheetsCount() >= 1) {
                        $rows = $xlsx->rowsFromTo(1, 0, 10);
                    }
                    $xlsx_html = "<table class='data_table_xlsx_number table table-striped table-bordered table-hover' ><tr>";
                    $xlsx_html .= "<th><i class='fa fa-list-ol'></i></th>";
                    for ($col = 'A'; $col < 'Z'; $col++) {
                        $xlsx_html .= "<th><span class='btn'>";
                        $xlsx_html .= "<i style='cursor:pointer' id='{$col}' onclick=\"add_var('{$col}')\" class='fa fa-2x fa-arrow-circle-down'></i>";
                        $xlsx_html .= "&nbsp;&nbsp;{$col}&nbsp;&nbsp;";
                        $xlsx_html .= "<input type='radio' name='number_index' value='" . (ord($col) - 65) . "' " . ($col == 'A' ? "checked='checked'" : "") . "/></span></th>";
                    }
                    $xlsx_html .= "</tr>";
                    foreach ($rows as $row) {
                        $xlsx_html .= "<tr><td></td>";
                        for ($col = 0; $col < 26; $col++) {
                            $xlsx_html .= "<td>" . (empty($row[$col]) ? "" : $row[$col]) . "</td>";
                        }
                        $xlsx_html .= "</tr>";
                    }
                    $xlsx_html .= "</table>";
                    echo json_encode(array("response" => "success", "sms_type" => "VARIABLES", "text" => $xlsx_html));
                } else {
                    echo json_encode(array("response" => "success", "sms_type" => "NORMAL", "text" => $this->lang->line('msg_success_saving')));
                }
                return;
            } else {
                echo json_encode(array("response" => "error", "text" => $this->lang->line('msg_error_excel')));
                return;
            }
        }
    }

    public function statistics_old()
    {
        if ($this->session->userdata('user_logged_in')) {
            $this->load->model('user');
            $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
            $all_numbers = $this->input->post('all_numbers');
            $sender_name = $this->input->post('sender_name');
            $message = $this->input->post('message');
            $send_time_method = $this->input->post('send_time_method');
            $send_time = $this->input->post('send_time');
            $sms_type = $this->input->post('sms_type');
            $number_index = $this->input->post('number_index');
            $repeation_period = $this->input->post('repeation_period');
            $repeation_times = $this->input->post('repeation_times');
            if (!empty($all_numbers) && !empty($sender_name) && !empty($message) && !empty($send_time_method)) {
                if (!$user->active) {
                    echo $this->lang->line('msg_error_inactive_user');
                } elseif (!$this->check_message($message)) {
                    echo $this->lang->line('msg_error_message_with_bad_words');
                } elseif (!empty($user->balance_expire_date) && $user->balance_expire_date < date_format(new DateTime(), 'Y-m-d')) {
                    echo $this->lang->line('msg_error_expired_blance');
                } else {
                    $excel_file_numbers = $this->session->userdata('excel_file_numbers');
                    $excel_file_path = empty($excel_file_numbers) ? "" : "./uploads/{$excel_file_numbers}";
                    if (!get_file_oss($excel_file_path)) {
                        $this->db->trans_rollback();
                        die(json_encode(['type' => "danger", 'text' => "Outbox excel not found"]));
                    }
                    $this->load->model('messagedetails');
                    $this->messagedetails->create_statistics_temp_table(
                        $this->session->userdata('user_logged_info')['id'],
                        $message,
                        $all_numbers,
                        ($sms_type == "VARIABLES"),
                        $excel_file_path,
                        (empty($number_index) ? 0 : $number_index)
                    );

                    $data = array(
                        'entries' => $this->messagedetails->get_statistics($this->session->userdata('user_logged_info')['id']),
                        'all_numbers' => $all_numbers,
                        'sender_name' => $sender_name,
                        'message' => $message,
                        'send_time_method' => $send_time_method,
                        'send_time' => $send_time,
                        'repeation_period' => $repeation_period,
                        'repeation_times' => $repeation_times,
                        'sms_type' => $sms_type,
                        'number_index' => $number_index,
                        'user_balance' => ($user->total_balance + $user->credit_limit - $user->spent_balance)
                    );
                    $this->messagedetails->drop_statistics_temp_table($this->session->userdata('user_logged_info')['id']);
                    //Add to session
                    $this->session->set_userdata(['send_sms_info' => $data]);
                    $this->load->view('user/form_statistics_variable', $data);
                }
            } else {
                echo $this->lang->line('msg_error_saving');
            }
        } else {
            $this->load->view('user/form_login', array('current_path' => 'user/sms'));
        }
    }

    /* This is function for calculate sms cost
     * @param $_POST
     * @return country wise cose
     */

    public function statistics()
    {
        $this->prof_flag("Done");
        //Check authenticate user
        if (!$this->session->userdata('user_logged_in')) {
            $this->load->view('user/form_login', array('current_path' => 'user/sms'));
            exit;
        }
        $request = $this->input->post();
        // $this->page_data['warning']['warning'] ="s";
        $decodedMessage = rawurldecode($request['message']);

        $request['message'] = str_replace(
            ['[percent]', '[at]', '[colon]', '[question]', '[equals]', '[amp]'],
            ['%', '@', ':', '?', '=', '&'],
            $decodedMessage
        );

        // Extract the context part if necessary


        $user_info = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
        $this->prof_flag("f1");
        //Check validation
        if (empty($request['all_numbers']) || empty($request['sender_name']) || empty($request['message']) || empty($request['send_time_method'])) {
            die($this->lang->line('msg_error_saving'));
        }

        if (!$user_info->active) {
            die($this->lang->line('msg_error_inactive_user'));
        } elseif (!$this->check_message($request['message'])) {
            die($this->lang->line('msg_error_message_with_bad_words'));
        } elseif (!empty($user_info->balance_expire_date) && $user_info->balance_expire_date < date_format(new DateTime(), 'Y-m-d')) {
            die($this->lang->line('msg_error_expired_blance'));
        }

        //Get country info
        $get_countries = $this->country->get_active_countries_by_user_v2($user_info->id, $user_info->is_international);
        $this->prof_flag("f2");
        //$this->country->get_active_countries();
        //
        //Get operators info
        $operators = $this->operator->get_active_operators(array_column($get_countries, 'id'));
        //TODO:: Need to handle standard way to excel file
        $excel_file_numbers = $this->session->userdata('excel_file_numbers');
        $excel_file_path = empty($excel_file_numbers) ? NULL : "./uploads/{$excel_file_numbers}";
        $this->load->helper('message');
        $message_long = calc_message_length($request['message'], $this->setting->get_value_by_name('enter_length'), $request['sms_type']);
        $all_numbers = trim($request['all_numbers']);
        $all_numbers = str_replace(",", "\n", $all_numbers);
        $all_numbers = str_replace('"', "", $all_numbers);
        $all_numbers = explode("\n", $all_numbers);
        $all_numbers = array_filter($all_numbers, [$this, "checkIfEmpty"]);

        $all_numbers = array_unique($all_numbers);
        // $all_numbers = array_filter(explode(',', $request['all_numbers']));
        $number_arr = [];
        $entries = [];
        $num_variable_message = [];
        $this->prof_flag("f3");
        foreach ($all_numbers as $number) {
            if (is_numeric($number)) { //Number
                if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                    $number = "966" . substr($number, 1);
                } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                    $number = "966" . $number;
                }

                if (!isset($number_arr[$number])) {
                    //Get country & cost
                    $found = false;
                    foreach ($get_countries as $key => $country) {
                        preg_match("/^{$country['id']}/", substr($number, 0, 5), $match);
                        if ($match) {
                            $found = true;
                            if ((strlen($number) >= $country['min_number_count']) && (strlen($number) <= $country['max_number_count'])) {
                                $entries[$country['id']] = [
                                    'id' => $country['id'],
                                    'name_ar' => $country['name_ar'],
                                    'name_en' => $country['name_en'],
                                    'coverage_status' => $country['coverage_status'],
                                    'cnt' => (isset($entries[$country['id']]['cnt'])) ? ((int) $entries[$country['id']]['cnt'] + 1) : 1,
                                    'cost' => (!empty($entries[$country['id']]['cost']) ? (float) $entries[$country['id']]['cost'] : 0),
                                ];

                                $match_status = false;
                                if (!empty($operators[$country['id']])) {
                                    foreach ($operators[$country['id']] as $operator) {
                                        //Operator foreach
                                        $codes = array_filter(explode(",", $operator['code']));
                                        foreach ($codes as $code) {
                                            //Code foreach
                                            preg_match("/^{$code}/", substr($number, strlen($country['id']), 3), $match_operator);
                                            if (!empty($match_operator)) {
                                                $match_status = true;
                                                $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($operator['price'] * $message_long);
                                                break;
                                            }
                                        } //End code foreach

                                        if ($match_status) {
                                            break;
                                        }
                                    }
                                    //End operator foreach
                                } //End if operators

                                if (!$match_status) {
                                    //Add to cost
                                    $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($country['price'] * $message_long);
                                }

                                // //Add to central array
                                // array_merge($number_arr, (array)$number);
                                $number_arr[$number] = $number;
                                // var_dump($number_arr);
                                continue;
                            }
                        }
                    }
                    //End foreach
                    if (!$found) {
                        $entries[0] = [
                            'id' => $country['id'],
                            'name_ar' => "غير محدد",
                            'name_en' => "undefined",
                            'coverage_status' => 0,
                            'cnt' => (!empty($entries[0]['cnt']) ? (int) $entries[0]['cnt'] : 0),
                            'cost' => (!empty($entries[0]['cost']) ? (float) $entries[0]['cost'] : 0),
                        ];
                    }
                }
            } elseif (!empty($number) && ($number[0] == "G")) { //Group
                $group_id = substr($number, 1);
                if ($group_id != 0) {
                    $groups = $this->contactgroup->get_by_group_id($group_id);
                } else {
                    $groups = null;
                }
                $group_contacts = $this->contact->get_group_contact($group_id, $user_info->id);

                foreach ($groups as $group) {

                    $group_contacts = array_merge($group_contacts == null ? [] : $group_contacts, $this->contact->get_group_contact($group->id, $user_info->id) == null ? [] : $this->contact->get_group_contact($group->id, $user_info->id));


                }

                if (!empty($group_contacts)) {
                    foreach ($group_contacts as $contacts) {
                        if (!isset($number_arr[$contacts['number']])) {
                            $found = false;
                            //Get country & cost
                            foreach ($get_countries as $key => $country) {
                                if (substr($contacts['number'], 0, 2) == "05" && strlen($contacts['number']) == 10) {
                                    $contacts['number'] = "966" . substr($contacts['number'], 1);
                                } elseif (substr($contacts['number'], 0, 1) == "5" && strlen($contacts['number']) == 9) {
                                    $contacts['number'] = "966" . $contacts['number'];
                                }
                                preg_match("/^{$country['id']}/", substr($contacts['number'], 0, 5), $match);
                                if (!empty($match)) {
                                    $found = true;
                                    if ((strlen($contacts['number']) >= $country['min_number_count']) && (strlen($contacts['number']) <= $country['max_number_count'])) {
                                        $entries[$country['id']] = [
                                            'id' => $country['id'],
                                            'name_ar' => $country['name_ar'],
                                            'name_en' => $country['name_en'],
                                            'coverage_status' => $country['coverage_status'],
                                            'cnt' => (int) $entries[$country['id']]['cnt'] + 1,
                                            'cost' => (!empty($entries[$country['id']]['cost']) ? (float) $entries[$country['id']]['cost'] : 0),
                                        ];

                                        $match_status = false;
                                        if (!empty($operators[$country['id']])) {
                                            foreach ($operators[$country['id']] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts['number'], strlen($country['id']), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($operator['price'] * $message_long);
                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($country['price'] * $message_long);
                                        }

                                        //Add to central array
                                        $number_arr[$contacts['number']] = $contacts['number'];
                                        continue;
                                    } //End if
                                } //End if
                            } //End foreach
                            if (!$found) {
                                $entries[0] = [
                                    'id' => $country['id'],
                                    'name_ar' => "غير محدد",
                                    'name_en' => "undefined",
                                    'coverage_status' => 0,
                                    'cnt' => (int) $entries[0]['cnt'] + 1,
                                    'cost' => (!empty($entries[0]['cost']) ? (float) $entries[0]['cost'] : 0),
                                ];
                            }
                        } //End if
                    } //End foreach
                } //End if
            } elseif ($number[0] == "A") { //Add Contact
                $tag_id = substr($number, 1);
                $ad_contacts = $this->adcontact->get_add_contact($tag_id);
                if (!empty($ad_contacts)) {
                    foreach ($ad_contacts as $contacts) {
                        if (!isset($number_arr[$contacts['number']])) {
                            //Get country & cost
                            foreach ($get_countries as $key => $country) {
                                preg_match("/^{$country['id']}/", substr($contacts['number'], 0, 5), $match);
                                if (!empty($match)) {
                                    if ((strlen($contacts['number']) >= $country['min_number_count']) && (strlen($contacts['number']) <= $country['max_number_count'])) {
                                        $entries[$country['id']] = [
                                            'id' => $country['id'],
                                            'name_ar' => $country['name_ar'],
                                            'name_en' => $country['name_en'],
                                            'coverage_status' => $country['coverage_status'],
                                            'cnt' => (int) $entries[$country['id']]['cnt'] + 1,
                                            'cost' => (!empty($entries[$country['id']]['cost']) ? (float) $entries[$country['id']]['cost'] : 0),
                                        ];

                                        $match_status = false;
                                        if (!empty($operators[$country['id']])) {
                                            foreach ($operators[$country['id']] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts['number'], strlen($country['id']), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($operator['price'] * $message_long);
                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($country['price'] * $message_long);
                                        }

                                        //Add to central array
                                        $number_arr[$contacts['number']] = $contacts['number'];
                                        // array_push($number_arr, $contacts['number']);
                                        continue;
                                    } //End if
                                } //End if
                            } //End foreach
                        } //End if
                    } //End foreach
                }
            } elseif ($number[0] == "S") { //Repeat message
                $repeat_numbers = $this->messagedetails->get_repeat_numbers(substr($request['all_numbers'], 1));
                if (!empty($repeat_numbers)) {
                    foreach ($repeat_numbers as $contacts) {
                        if (!in_array($contacts['number'], $number_arr)) {
                            //Get country & cost
                            foreach ($get_countries as $key => $country) {
                                preg_match("/^{$country['id']}/", substr($contacts['number'], 0, 5), $match);
                                if (!empty($match)) {
                                    if ((strlen($contacts['number']) >= $country['min_number_count']) && (strlen($contacts['number']) <= $country['max_number_count'])) {
                                        $entries[$country['id']] = [
                                            'id' => $country['id'],
                                            'name_ar' => $country['name_ar'],
                                            'name_en' => $country['name_en'],
                                            'coverage_status' => $country['coverage_status'],
                                            'cnt' => (int) $entries[$country['id']]['cnt'] + 1,
                                            'cost' => (!empty($entries[$country['id']]['cost']) ? (float) $entries[$country['id']]['cost'] : 0),
                                        ];

                                        $match_status = false;
                                        if (!empty($operators[$country['id']])) {
                                            foreach ($operators[$country['id']] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts['number'], strlen($country['id']), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($operator['price'] * $message_long);
                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($country['price'] * $message_long);
                                        }

                                        //Add to central array
                                        array_push($number_arr, $contacts['number']);
                                        continue;
                                    } //End if
                                } //End if
                            } //End foreach
                        } //End if
                    } //End foreach
                }
            } elseif ($number[0] == "U") { //Unknown
                die("Unknow type sms please contact with system admin");
            } elseif ($number[0] == "F") { //Unknown
                die("Unknow type sms please contact with system admin");
            } elseif (($number == "excel_file") && (!empty($excel_file_path))) { //Excel file
                if (!get_file_oss($excel_file_path)) {
                    $this->db->trans_rollback();
                    die(json_encode(['type' => "danger", 'text' => "Outbox excel not found"]));
                }
                if ($user_info->id == 8 ) {
                    $response_data = $this->createBackgroundJob($request, $user_info, []);
                    header('Content-Type: application/json; charset=utf-8');
                    die(json_encode($response_data));
                }


                //   die('s');
                $this->load->library('simplexlsx');
                $xlsx = new SimpleXLSX($excel_file_path);
                $rows_cnt = 0;
                for ($sheetsCounter = 1; $sheetsCounter <= $xlsx->sheetsCount(); $sheetsCounter++) {
                    $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, 10000);
                    while (!empty($rows)) {
                        foreach ($rows as $value) {
                            $number = $value[0];
                            if (is_numeric($number)) {
                                if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                                    $number = "966" . substr($number, 1);
                                } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                                    $number = "966" . $number;
                                }

                                //Variable message
                                if ($request['sms_type'] == "VARIABLES") {
                                    $variables_message_text = $request['message'];
                                    for ($col = 'A'; $col < 'Z'; $col++) {
                                        if (isset($value[ord($col) - 65])) {
                                            $variables_message_text = str_replace("{{$col}}", $value[ord($col) - 65], $variables_message_text);
                                        }
                                    }

                                    $num_variable_message[$number] = [
                                        'message' => $variables_message_text,
                                        'length' => calc_message_length($variables_message_text, $this->setting->get_value_by_name('enter_length')),
                                    ];
                                }
                            }

                            if (!isset($number_arr[$number])) {
                                //Handle variable message length
                                if ($request['sms_type'] == "VARIABLES") {
                                    $message_long = $num_variable_message[$number]['length'];
                                }
                                $found = false;
                                //Get country & cost
                                foreach ($get_countries as $key => $country) {
                                    preg_match("/^{$country['id']}/", substr($number, 0, 5), $match);
                                    if (!empty($match)) {
                                        $found = true;
                                        if ((strlen($number) >= $country['min_number_count']) && (strlen($number) <= $country['max_number_count'])) {
                                            $entries[$country['id']] = [
                                                'id' => $country['id'],
                                                'name_ar' => $country['name_ar'],
                                                'name_en' => $country['name_en'],
                                                'coverage_status' => $country['coverage_status'],
                                                'cnt' => (isset($entries[$country['id']]['cnt'])) ? ((int) $entries[$country['id']]['cnt'] + 1) : 1,
                                                'cost' => (!empty($entries[$country['id']]['cost']) ? (float) $entries[$country['id']]['cost'] : 0),
                                            ];

                                            $match_status = false;
                                            if (!empty($operators[$country['id']])) {
                                                foreach ($operators[$country['id']] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($number, strlen($country['id']), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($operator['price'] * $message_long);
                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                //Add to cost
                                                $entries[$country['id']]['cost'] = (float) $entries[$country['id']]['cost'] + ($country['price'] * $message_long);
                                            }

                                            //Add to central array
                                            $number_arr[$number] = $number;
                                            //   $number_arr =   array_merge((array)$number,$number_arr);
                                            // var_dump($number_arr);
                                            // echo "<br>";
                                            // die();
                                            continue;
                                        } //End if
                                    } //End if
                                } //End foreach
                                if (!$found) {
                                    $entries[0] = [
                                        'id' => $country['id'],
                                        'name_ar' => "غير محدد",
                                        'name_en' => "undefined",
                                        'coverage_status' => 0,
                                        'cnt' => (!empty($entries[0]['cnt']) ? (int) $entries[0]['cnt'] : 0),
                                        'cost' => (!empty($entries[0]['cost']) ? (float) $entries[0]['cost'] : 0),
                                    ];
                                }
                            }
                        } //End foreach
                        $rows_cnt += 10000;
                        $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, 10000);
                    } //End while loop
                } //End for loop
            }
        } //End number foreach
        $this->prof_flag("f4");
        $data = array(
            'entries' => array_values($entries),
            'all_numbers' => implode(',', $number_arr),
            'sender_name' => $request['sender_name'],
            'message' => $request['message'],
            'send_time_method' => $request['send_time_method'],
            'send_time' => $request['send_time'],
            'repeation_period' => $request['repeation_period'],
            'repeation_times' => $request['repeation_times'],
            'sms_type' => $request['sms_type'],
            'number_index' => 0,
            'user_balance' => (($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance),
            'calendar_time' => $request['calendar_time'] ?? null,
            'reminder' => $request['reminder'] ?? null,
            'reminder_text' => $request['reminder_text'] ?? null,
            'google_map_url' => $request['google_map_url'] ?? null
        );

        $count_all = 0;
        for ($i = 0; $i < count($data['entries']); $i++) {
            if ($data['entries'][$i]["coverage_status"] == 1) {
                $count_all += $data['entries'][$i]["cnt"];
            }
        }

        if ($count_all > 100000) {
            die("عذراً , لقد تجاوزت الحد الأقصى  100000 رقم في الارسالية");
        }
        // if($request['sender_name'] == "virtual"){
        //     var_dump($this->prof_print());
        //     die();
        // }
        //Add to session

        $this->session->set_userdata(['send_sms_info' => $data]);
        $this->load->view('user/form_statistics', $data);
    }

    /*
motaz
*/
    public function checkIfMobile($mobile)
    {
        $mobile = filter_var($mobile, FILTER_SANITIZE_NUMBER_INT);
        if ($mobile === false || strlen($mobile) == 0) {
            return false;
        }
        $mobile = str_replace(['+', '-'], '', $mobile);
        return true;
    }

    public function checkIfEmpty($mobile)
    {
        if (strlen(trim($mobile)) == 0)
            return false;
        return true;

    }

    //    public function send() {
//        if ($this->session->userdata('user_logged_in')) {
//            $this->load->model('user');
//            $user = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
//            $all_numbers = $this->input->post('all_numbers');
//            $sender_name = $this->input->post('sender_name');
//            $message = $this->input->post('message');
//            $send_time_method = $this->input->post('send_time_method');
//            $send_time = $this->input->post('send_time');
//            $sms_type = $this->input->post('sms_type');
//            $number_index = $this->input->post('number_index');
//            $repeation_period = $this->input->post('repeation_period');
//            $repeation_times = $this->input->post('repeation_times');
//            //Encrypt mobile number
//            $encrypt_number = 0;
//            if (!empty($sms_type)) {
//                $encrypt_number = $this->encrypt_number_status($sms_type);
//            }
//
//            if (!empty($all_numbers) && !empty($sender_name) && !empty($message) && !empty($send_time_method)) {
//                if (!$user->active) {
//                    $this->message = array('type' => 'danger', 'text' => $this->lang->line('msg_error_inactive_user'));
//                } elseif (!$this->check_message($message)) {
//                    $this->message = array('type' => 'danger', 'text' => $this->lang->line('msg_error_message_with_bad_words'));
//                } elseif (!empty($user->balance_expire_date) && $user->balance_expire_date < date_format(new DateTime(), 'Y-m-d')) {
//                    $this->message = array('type' => 'danger', 'text' => $this->lang->line('msg_error_expired_blance'));
//                } else {
//
//                    $auth_code = rand(1000000, 9999999);
//                    $this->load->model('outbox');
//                    //Save to outbox tb
//                    $entry_id = $this->outbox->insert_by_array(array(
//                        'id' => null,
//                        'channel' => 'DIRECT',
//                        'user_id' => $this->session->userdata('user_logged_info')['id'],
//                        'text' => $message,
//                        'creation_datetime' => date("Y-m-d h:i:s"),
//                        'sending_datetime' => $send_time_method == "LATER" ? $send_time : null,
//                        'repeation_period' => $send_time_method == "LATER" ? $repeation_period : "0",
//                        'repeation_times' => $send_time_method == "LATER" ? $repeation_times : 0,
//                        'variables_message' => ($sms_type == "VARIABLES" ? 1 : 0),
//                        'sender_name' => $sender_name,
//                        'excel_file_numbers' => $this->session->userdata('excel_file_numbers'),
//                        'all_numbers' => $all_numbers,
//                        'number_index' => $number_index,
//                        'auth_code' => $auth_code
//                    ));
//
//                    $this->load->library('curl');
//                    $send_sms = $this->curl->_simple_call("get", site_url("user/sms/pre_flush_by_auth_code/{$entry_id}/{$auth_code}/{$encrypt_number}/"), array(), array("TIMEOUT" => 3));
//                    $response = json_decode($send_sms, TRUE);
//                    if ($response['type'] == "error") {
//                        $this->message = $response;
//                    } else { //Error
//                        $outbox_href = "<a href='" . site_url('user/outboxes') . "'>" . $this->lang->line('lbl_outbox') . "</a>";
//                        $sent_messages_href = "<a href='" . site_url('user/smsarchive') . "'>" . $this->lang->line('lbl_sent_messages') . "</a>";
//                        $this->message = array(
//                            'type' => 'success',
//                            'text' => str_replace("{sent_messeages_href}", $sent_messages_href, str_replace("{outbox_href}", $outbox_href, $this->lang->line('msg_success_outboxing')))
//                        );
//                    }
//
//                    //Redirect page
//                    $sms_url = "user/sms";
//                    if ($sms_type == "VARIABLES")
//                        $sms_url .= "/variables";
//                    elseif ($sms_type == "ADS")
//                        $sms_url .= "/ads";
//                    elseif ($sms_type == "VOICE")
//                        $sms_url .= "/voicecall";
//
//                    $this->session->set_flashdata('message', $this->message);
//                    redirect($sms_url);
//                }
//            } else {
//                echo $this->lang->line('msg_error_saving');
//            }
//        } else {
//            redirect('');
//        }
//    }

    /*
     * New Send function
     */

    /*
     * Calculate & send function
     * @param $_POST
     * @return send sms
     */

    public function send()
    {
        $this->prof_flag("Done");

        //Check login user
        if (!$this->session->userdata('user_logged_in')) {
            $this->message = ['type' => 'danger', 'text' => "Please login first"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        $request = $this->input->post();

        if ($request['use_session'] == 1) {
            $request = $this->session->userdata('send_sms_info');
        }
        $user_info = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
        if (count($user_info->get_senders()) == 0 && $user_info->get_granted_senders() == 0) {
            $request['sender_name'] = 'Dreams';
            $request['message'] = "رسالة تجريبية من موقع dreams.sa";
        }
        //Encrypt mobile number
        $encrypt_number = 0;
        if (!empty($request['sms_type'])) {
            $encrypt_number = $this->encrypt_number_status($request['sms_type']);

        }
        //Check validation

        if (empty($request['all_numbers']) || empty($request['sender_name']) || empty($request['message']) || empty($request['send_time_method'])) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_saving')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }
        // if (strpos($request['sender_name'], '-AD') && $request['sender_name'] != 'AneisLab-AD' && $request['sender_name'] != 'ALASHAL-AD'){
        //     $this->message = ['type' => 'danger', 'text' => "لايمكن ارسال رسائل دعائية في هذا الوقت"];
        //         $this->session->set_flashdata('message', $this->message);
        //         redirect('user/sms');
        // }
        if (strpos($request['sender_name'], '-AD') && $request['send_time_method'] != "LATER") {

            if (strtotime($this->server_time()) > strtotime("22:00:00") || strtotime($this->server_time()) < strtotime("09:00:00")) {
                $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_time_exceeded')];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }

        }
        if (strpos($request['sender_name'], '-AD') && $request['send_time_method'] == "LATER") {
            $send_time = $request['send_time'];

            $dt = new DateTime($send_time);

            $time = $dt->format('H:i');
            // if ($time < strtotime("12:00:00") && $time > strtotime("01:00:00")) {
            if (strtotime($time) > strtotime("22:00") || strtotime($time) < strtotime("09:00")) {
                $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_time_exceeded')];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }

        }

        $advertising_words = false;
        if ($this->check_advertising_words_message($request['message']) && !strpos($request['sender_name'], '-AD')) {
            $advertising_words = true;
        }
        if ($this->check_url_in_message($request['message']) && $user_info->allow_url == 0) {
            $advertising_words = true;
        }

        if (!$user_info->active) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_inactive_user')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        } elseif (!$this->check_message($request['message'])) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_message_with_bad_words')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        } elseif (!empty($user_info->balance_expire_date) && $user_info->balance_expire_date < date_format(new DateTime(), 'Y-m-d')) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_expired_blance')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        //Get country info
        $get_countries = $this->country->get_active_countries_by_user_v2($user_info->id, $user_info->is_international);
        //$this->country->get_active_countries();
        $countries = array_column($get_countries, 'price', 'id');
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');
        //Get operators info
        $operators = $this->operator->get_active_operators(array_keys($countries));

        //TODO:: Need to handle standard way to excel file
        $excel_file_numbers = $this->session->userdata('excel_file_numbers');
        $excel_file_path = empty($excel_file_numbers) ? NULL : "./uploads/{$excel_file_numbers}";
        //Message info
        $this->load->helper('message');
        if ($request['sms_type'] == 'CALENDAR') {

            $coordinates = get_lat_long($request['google_map_url']);
            $calendar = get_calendar_url($user_info->id, $request['sender_name'], $request['message'], $request['calendar_time'], $request['reminder'], $request['reminder_text'], $coordinates['lat'], $coordinates['long'], extractLocationTitle($request['google_map_url']));
            if ($calendar['status'] <> 1) {
                $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_calendar_api')];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }
            $request['message'] .= "\n" . $calendar['url'];

        }
        $message_long = calc_message_length($request['message'], $this->setting->get_value_by_name('enter_length'));
        // echo $request['message'];
        //Send limit
        $this->real_time_send_limit = $this->page_data['inner_page_data']['site_settings']['real_time_send_limit'];
        //Prepare number
        $request['all_numbers'] = str_replace(",", "\n", $request['all_numbers']);
        $this->prof_flag("Prepare");
        $all_numbers = array_filter(explode("\n", $request['all_numbers']));
        $all_numbers = explode("\n", $request['all_numbers']);
        $all_numbers = array_filter($all_numbers, [$this, "checkIfEmpty"]);

        $all_numbers = array_unique($all_numbers);
        // $all_numbers = array_filter(explode('\n', $request['all_numbers']));
        $number_arr = [];
        $cost = [];
        $operator_id = [];
        $country_ids = [];
        $num_variable_message = [];

        //Prepare session param
        $session_sms_info = $this->session->userdata('send_sms_info');
        $cov_entries = array_filter($session_sms_info['entries'], function ($item) {
            if ($item['coverage_status'] == 1) {
                return true;
            }
            return false;
        });

        $total_numbers = array_column($cov_entries, 'cnt');

        // $sender = $this->sender->get_by_user_id_name($user_info->id, $request['sender_name']);
        // if($sender){

        //     $sms_daily = $this->message->get_count_day_by_sender_name($request['sender_name']);
        //     if($request['sender_name'] == "Dreams-AD" && $sender->max_sms_one_day != null && ($sender->max_sms_one_day+$total_numbers[0]) > $sms_daily ){
        //         $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_insufficient_balance')];
        //         $this->session->set_flashdata('message', $this->message);
        //         redirect('user/sms');
        //     }
        // }

        // check count numbers in whitelist
        if (!strpos($request['sender_name'], '-AD') && $user_info->send_bloks_status == 0 && $this->check_sender_count_msg($total_numbers[0])) {
            $advertising_words = true;
        }

        $this->prof_flag("Prepare2");


        //Large number send
        if (array_sum($total_numbers) > $this->real_time_send_limit) {
            $cost = array_column($cov_entries, 'cost');
            $number_arr = $total_numbers;
        } else {
            foreach ($all_numbers as $number) {
                $number = preg_replace('/\s+/', '', $number);

                if (is_numeric(preg_replace('/\s+/', '', $number))) { //Number
                    if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                        $number = "966" . substr($number, 1);
                    } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                        $number = "966" . $number;
                    }

                    if (!isset($number_arr[$number])) {
                        //Get price by country
                        foreach ($countries as $key => $price) {
                            preg_match("/^{$key}/", substr($number, 0, 5), $match);
                            if (!empty($match)) {
                                if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                                    $match_status = false;
                                    if (!empty($operators[$key])) {
                                        foreach ($operators[$key] as $operator) { //Operator foreach
                                            $codes = array_filter(explode(",", $operator['code']));
                                            foreach ($codes as $code) { //Code foreach
                                                preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                                if (!empty($match_operator)) {
                                                    $match_status = true;
                                                    $cost[$number] = ($operator['price'] * $message_long);
                                                    $operator_id[$number] = $operator['id'];
                                                    break;
                                                }
                                            } //End code foreach

                                            if ($match_status) {
                                                break;
                                            }
                                        } //End operator foreach
                                    } //End if operators

                                    if (!$match_status) {
                                        //Add to cost
                                        $cost[$number] = ($price * $message_long);
                                    }

                                    //Add country id
                                    $country_ids[$number] = $match[0];
                                    //Add to central array
                                    // echo $number;
                                    $number_arr[$number] = $number;
                                    // array_push($number_arr, $number);
                                    // var_dump($number_arr);
                                    continue;
                                }
                            }
                        } //End foreach
                    }
                } elseif (!empty($number) && ($number[0] == "G")) { //Group
                    $group_id = substr($number, 1);
                    $group_contacts = $this->contact->get_group_contact($group_id, $user_info->id);
                    if ($group_id != 0) {
                        $groups = $this->contactgroup->get_by_group_id($group_id);
                    } else {
                        $groups = null;
                    }
                    foreach ($groups as $group) {
                        $group_contacts = array_merge($group_contacts, $this->contact->get_group_contact($group->id, $user_info->id));
                    }
                    if (!empty($group_contacts)) {
                        foreach ($group_contacts as $contacts) {
                            if (substr($contacts['number'], 0, 2) == "05" && strlen($contacts['number']) == 10) {
                                $contacts['number'] = "966" . substr($contacts['number'], 1);
                            } elseif (substr($contacts['number'], 0, 1) == "5" && strlen($contacts['number']) == 9) {
                                $contacts['number'] = "966" . $contacts['number'];
                            }

                            if (!in_array($contacts['number'], $number_arr)) {
                                //Get price by country
                                foreach ($countries as $key => $price) {
                                    preg_match("/^{$key}/", substr($contacts['number'], 0, 5), $match);
                                    if (!empty($match)) {
                                        if ((strlen($contacts['number']) >= $country_min_number_count[$key]) && (strlen($contacts['number']) <= $country_max_number_count[$key])) {
                                            $match_status = false;
                                            if (!empty($operators[$key])) {
                                                foreach ($operators[$key] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($contacts['number'], strlen($key), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $cost[$contacts['number']] = ($operator['price'] * $message_long);
                                                            $operator_id[$contacts['number']] = $operator['id'];
                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                //Add to cost
                                                $cost[$contacts['number']] = ($price * $message_long);
                                            }

                                            //Add country id
                                            $country_ids[$contacts['number']] = $match[0];
                                            //Add to central array
                                            array_push($number_arr, $contacts['number']);
                                            continue;
                                        }
                                    }
                                } //End foreach
                            } //End if
                        } //End foreach
                    } //End if
                } elseif ($number[0] == "A") { //Add Contact
                    $tag_id = substr($number, 1);
                    $ad_contacts = $this->adcontact->get_add_contact($tag_id);
                    if (!empty($ad_contacts)) {
                        foreach ($ad_contacts as $contacts) {
                            if (!in_array($contacts['number'], $number_arr)) {
                                //Get price by country
                                foreach ($countries as $key => $price) {
                                    preg_match("/^{$key}/", substr($contacts['number'], 0, 5), $match);
                                    if (!empty($match)) {
                                        if ((strlen($contacts['number']) >= $country_min_number_count[$key]) && (strlen($contacts['number']) <= $country_max_number_count[$key])) {
                                            $match_status = false;
                                            if (!empty($operators[$key])) {
                                                foreach ($operators[$key] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($contacts['number'], strlen($key), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $cost[$contacts['number']] = ($operator['price'] * $message_long);
                                                            $operator_id[$contacts['number']] = $operator['id'];

                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                //Add to cost
                                                $cost[$contacts['number']] = ($price * $message_long);
                                            }

                                            //Add country id
                                            $country_ids[$contacts['number']] = $match[0];
                                            //Add to central array
                                            array_push($number_arr, $contacts['number']);
                                            continue;
                                        }
                                    }
                                } //End foreach
                            } //End if
                        } //End foreach
                    }
                } elseif ($number[0] == "S") { //Repeat message
                    $repeat_numbers = $this->messagedetails->get_repeat_numbers(substr($request['all_numbers'], 1));
                    if (!empty($repeat_numbers)) {
                        foreach ($repeat_numbers as $contacts) {
                            if (!in_array($contacts['number'], $number_arr)) {
                                //Get price by country
                                foreach ($countries as $key => $price) {
                                    preg_match("/^{$key}/", substr($contacts['number'], 0, 5), $match);
                                    if (!empty($match)) {
                                        if ((strlen($contacts['number']) >= $country_min_number_count[$key]) && (strlen($contacts['number']) <= $country_max_number_count[$key])) {
                                            $match_status = false;
                                            if (!empty($operators[$key])) {
                                                foreach ($operators[$key] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($contacts['number'], strlen($key), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $cost[$contacts['number']] = ($operator['price'] * $message_long);
                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                //Add to cost
                                                $cost[$contacts['number']] = ($price * $message_long);
                                            }

                                            //Add country id
                                            $country_ids[$contacts['number']] = $match[0];
                                            //Add to central array
                                            array_push($number_arr, $contacts['number']);
                                            continue;
                                        }
                                    }
                                } //End foreach
                            } //End if
                        } //End foreach
                    }
                } elseif ($number[0] == "U") { //Unknown
                    $this->message = ['type' => 'danger', 'text' => "Unknow type sms please contact with system admin"];
                    $this->session->set_flashdata('message', $this->message);
                    redirect('user/sms');
                } elseif ($number[0] == "F") { //Unknown
                    $this->message = ['type' => 'danger', 'text' => "Unknow type sms please contact with system admin"];
                    $this->session->set_flashdata('message', $this->message);
                    redirect('user/sms');
                } elseif (($number == "excel_file") && (!empty($excel_file_path))) { //Excel file
                    $this->load->library('simplexlsx');
                    $xlsx = new SimpleXLSX($excel_file_path);
                    $rows_cnt = 0;
                    for ($sheetsCounter = 1; $sheetsCounter <= $xlsx->sheetsCount(); $sheetsCounter++) {
                        $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, $this->real_time_send_limit);
                        while (!empty($rows)) {
                            foreach ($rows as $value) {
                                $number = $value[0];
                                if (is_numeric($number)) {
                                    if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                                        $number = "966" . substr($number, 1);
                                    } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                                        $number = "966" . $number;
                                    }

                                    //Variable message
                                    if ($request['sms_type'] == "VARIABLES") {
                                        $variables_message_text = $request['message'];
                                        for ($col = 'A'; $col < 'Z'; $col++) {
                                            if (isset($value[ord($col) - 65])) {
                                                $variables_message_text = str_replace("{{$col}}", $value[ord($col) - 65], $variables_message_text);
                                            }
                                        }

                                        $num_variable_message[$number] = [
                                            'message' => $variables_message_text,
                                            'length' => calc_message_length($variables_message_text, $this->setting->get_value_by_name('enter_length')),
                                        ];
                                    }
                                }

                                if (!isset($number_arr[$number])) {
                                    //Variable message length handle
                                    if ($request['sms_type'] == "VARIABLES") {
                                        $message_long = $num_variable_message[$number]['length'];
                                    }
                                    //Get price by country
                                    foreach ($countries as $key => $price) {
                                        preg_match("/^{$key}/", substr($number, 0, 5), $match);
                                        if (!empty($match)) {
                                            if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                                                $match_status = false;
                                                if (!empty($operators[$key])) {
                                                    foreach ($operators[$key] as $operator) {
                                                        //Operator foreach
                                                        $codes = array_filter(explode(",", $operator['code']));
                                                        foreach ($codes as $code) {
                                                            //Code foreach
                                                            preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                                            if (!empty($match_operator)) {
                                                                $match_status = true;
                                                                $cost[$number] = ($operator['price'] * $message_long);
                                                                $operator_id[$number] = $operator['id'];
                                                                break;
                                                            }
                                                        } //End code foreach

                                                        if ($match_status) {
                                                            break;
                                                        }
                                                    } //End operator foreach
                                                } //End if operators

                                                if (!$match_status) {
                                                    //Add to cost
                                                    $cost[$number] = ($price * $message_long);
                                                }

                                                //Add country id
                                                $country_ids[$number] = $match[0];
                                                //Add to central array
                                                $number_arr[$number] = $number;
                                                // array_push($number_arr, $number);
                                                continue;
                                            }
                                        }
                                    } //End foreach
                                }
                            } //End foreach
                            $rows_cnt += $this->real_time_send_limit;
                            $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, $this->real_time_send_limit);
                        } //End while loop
                    } //End for loop
                    // $this->session->set_userdata('excel_file_numbers', "");
                }
            } //End number foreach
            //Check number validation
            if (empty($number_arr)) {
                $this->message = ['type' => 'danger', 'text' => "We not get any send number2"];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }

        }


        //Check user balance
        $total_cost = round(array_sum($cost), 1);
        if (!empty($request['repeation_period'])) { //Repeat message
            $total_cost = $total_cost * ($request['repeation_times'] + 1);
        }

        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_insufficient_balance')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();

        //Deduct balance
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            $this->message = ['type' => 'danger', 'text' => "Balance deduct failed"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }
        //Save to outbox
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'DIRECT',
            'user_id' => $this->session->userdata('user_logged_info')['id'],
            'text' => $request['message'],
            'count' => array_sum($total_numbers) > $this->real_time_send_limit ? array_sum($total_numbers) : count($number_arr),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $request['send_time_method'] == "LATER" ? $request['send_time'] : NULL,
            'repeation_period' => $request['send_time_method'] == "LATER" ? $request['repeation_period'] : "0",
            'repeation_times' => $request['send_time_method'] == "LATER" ? $request['repeation_times'] : 0,
            'variables_message' => ($request['sms_type'] == "VARIABLES" ? 1 : 0),
            'sender_name' => $request['sender_name'],
            'excel_file_numbers' => $this->session->userdata('excel_file_numbers'),
            'all_numbers' => implode(',', $all_numbers),
            //$request['all_numbers'],
            'number_index' => null,
            'encrypted' => $encrypt_number,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);

        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            $this->message = ['type' => 'danger', 'text' => "Insert failed to outbox"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        //Send sms
        $output_message = [];
        $send_sms = $this->pre_message_flush_by_auth_code($outbox_id, $auth_code, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message, $operator_id);
        if ($send_sms['type'] == "success") { //Success
            $this->db->trans_commit();
            if ($advertising_words) {
                if ($this->site_settings['advertising_message'] == "SMS" || $this->site_settings['advertising_message'] == "BOTH") {
                    $this->send_telegram_message($this->lang->line('msg_info_messages_review_msg') . " |Sender: " . $request['sender_name'] . " |message_id: #" . $send_sms['data']['message_id']);
                    // $recive_numbers = explode(",", $this->site_settings['receiver_number_advertising']);
                    // foreach ($recive_numbers as $recive_number) {
                    //     $this->send_sms_admin(
                    //         $this->site_settings['system_sms_sender'],
                    //         $recive_number,
                    //         strip_tags($this->lang->line('msg_info_messages_review_msg')),
                    //         0,
                    //         null
                    //     );
                    // }

                }
            } elseif (empty($request['send_time']) && empty($outbox_param['variables_message'])) {//Regular message
                //Send cambian
                $res = $this->send_by_auth_code($send_sms['data']['message_id'], $auth_code);
            }
            if (!strpos($request['sender_name'], '-AD')) {
                if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                    $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram'));

                    // $this->send_sms_admin(
                    //     $this->site_settings['system_sms_sender'],
                    //     $this->site_settings['receiver_number'],
                    //     strip_tags($this->lang->line('msg_info_messages_overload')),
                    //     0,
                    //     array('{username}' => $this->session->userdata('user_logged_info')['username'])
                    // );
                }
            }

            $outbox_href = "<a href='" . site_url('user/outboxes') . "'>" . $this->lang->line('lbl_outbox') . "</a>";
            $sent_messages_href = "<a href='" . site_url('user/smsarchive') . "'>" . $this->lang->line('lbl_sent_messages') . "</a>";
            $output_message = array(
                'type' => 'success',
                'text' => str_replace("{sent_messeages_href}", $sent_messages_href, str_replace("{outbox_href}", $outbox_href, $this->lang->line('msg_success_outboxing')))
            );
        } else { //Error
            $this->db->trans_rollback();
            if (empty($send_sms['text'])) {
                $send_sms['text'] = "There is an unknown error please contact with administrator";
            }
            $output_message = $send_sms;
        }

        //Redirect page
        $sms_url = "user/sms";
        if ($request['sms_type'] == "VARIABLES")
            $sms_url .= "/variables";
        elseif ($request['sms_type'] == "ADS")
            $sms_url .= "/ads";
        elseif ($request['sms_type'] == "VOICE")
            $sms_url .= "/voicecall";
        elseif ($request['sms_type'] == "CALENDAR")
            $sms_url .= "/voicecall";

        $this->session->set_flashdata('message', $output_message);
        $this->session->set_userdata('excel_file_numbers', "");
        redirect($sms_url);
    }

    public function send_variable()
    {

        //Check login user
        if (!$this->session->userdata('user_logged_in')) {
            $this->message = ['type' => 'danger', 'text' => "Please login first"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        $request = $this->input->post();

        if ($request['use_session'] == 1) {
            $request = $this->session->userdata('send_sms_info');
        }
        $user_info = $this->user->get_by_id($this->session->userdata('user_logged_info')['id']);
        if (count($user_info->get_senders()) == 0 && $user_info->get_granted_senders() == 0) {
            $request['sender_name'] = 'Dreams';
            $request['message'] = "رسالة تجريبية من موقع dreams.sa";
        }
        //Encrypt mobile number
        $encrypt_number = 0;
        if (!empty($request['sms_type'])) {
            $encrypt_number = $this->encrypt_number_status($request['sms_type']);

        }
        //Check validation

        if (empty($request['all_numbers']) || empty($request['sender_name']) || empty($request['message']) || empty($request['send_time_method'])) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_saving')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }
        // if (strpos($request['sender_name'], '-AD') && $request['sender_name']!="AneisLab-AD" && $request['sender_name'] != 'ALASHAL-AD'){
        //     $this->message = ['type' => 'danger', 'text' => "لايمكن ارسال رسائل دعائية في هذا الوقت"];
        //         $this->session->set_flashdata('message', $this->message);
        //         redirect('user/sms');
        // }
        if (strpos($request['sender_name'], '-AD') && $request['send_time_method'] != "LATER") {

            if (strtotime($this->server_time()) > strtotime("22:00:00") || strtotime($this->server_time()) < strtotime("09:00:00")) {
                $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_time_exceeded')];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }

        }
        if (strpos($request['sender_name'], '-AD') && $request['send_time_method'] == "LATER") {
            $send_time = $request['send_time'];

            $dt = new DateTime($send_time);

            $time = $dt->format('H:i');
            // if ($time < strtotime("12:00:00") && $time > strtotime("01:00:00"))
            if (strtotime($time) > strtotime("22:00") || strtotime($time) < strtotime("09:00")) {
                $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_time_exceeded')];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }

        }

        $advertising_words = false;
        if ($this->check_advertising_words_message($request['message']) && !strpos($request['sender_name'], '-AD')) {
            $advertising_words = true;
        }
        if ($this->check_url_in_message($request['message']) && $user_info->allow_url == 0) {
            $advertising_words = true;
        }

        if (!$user_info->active) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_inactive_user')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        } elseif (!$this->check_message($request['message'])) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_message_with_bad_words')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        } elseif (!empty($user_info->balance_expire_date) && $user_info->balance_expire_date < date_format(new DateTime(), 'Y-m-d')) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_expired_blance')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        //Get country info
        $get_countries = $this->country->get_active_countries_by_user_v2($user_info->id, $user_info->is_international);
        //$this->country->get_active_countries();
        $countries = array_column($get_countries, 'price', 'id');
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');
        //Get operators info
        $operators = $this->operator->get_active_operators(array_keys($countries));

        //TODO:: Need to handle standard way to excel file
        $excel_file_numbers = $this->session->userdata('excel_file_numbers');
        $excel_file_path = empty($excel_file_numbers) ? NULL : "./uploads/{$excel_file_numbers}";
        //Message info
        $this->load->helper('message');
        $enter_length = $this->setting->get_value_by_name('enter_length');
        $message_long = calc_message_length($request['message']);
        // echo $request['message'];
        //Send limit
        $this->real_time_send_limit = $this->page_data['inner_page_data']['site_settings']['real_time_send_limit'];
        //Prepare number
        $request['all_numbers'] = str_replace(",", "\n", $request['all_numbers']);
        $this->prof_flag("Prepare");
        $all_numbers = array_filter(explode("\n", $request['all_numbers']));
        $all_numbers = explode("\n", $request['all_numbers']);
        $all_numbers = array_filter($all_numbers, [$this, "checkIfEmpty"]);

        $all_numbers = array_unique($all_numbers);
        // $all_numbers = array_filter(explode('\n', $request['all_numbers']));
        $number_arr = [];
        $cost = [];
        $operator_id = [];
        $country_ids = [];
        $num_variable_message = [];

        //Prepare session param
        $session_sms_info = $this->session->userdata('send_sms_info');
        $cov_entries = array_filter($session_sms_info['entries'], function ($item) {
            if ($item['coverage_status'] == 1) {
                return true;
            }
            return false;
        });

        $total_numbers = array_column($cov_entries, 'cnt');

        // check count numbers in whitelist
        if (!strpos($request['sender_name'], '-AD') && $user_info->send_bloks_status == 0 && $this->check_sender_count_msg($total_numbers[0])) {
            $advertising_words = true;
        }

        //Large number send
        if (array_sum($total_numbers) > $this->real_time_send_limit) {
            $total_cost = round(array_sum(array_column($cov_entries, 'cost')));
            $number_arr = $total_numbers;
        } else {
            foreach ($all_numbers as $number) {
                $number = preg_replace('/\s+/', '', $number);

                if (($number == "excel_file") && (!empty($excel_file_path))) { //Excel file
                    $this->load->library('simplexlsx');
                    $xlsx = new SimpleXLSX($excel_file_path);
                    $rows_cnt = 0;
                    for ($sheetsCounter = 1; $sheetsCounter <= $xlsx->sheetsCount(); $sheetsCounter++) {
                        $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, $this->real_time_send_limit);
                        while (!empty($rows)) {
                            foreach ($rows as $value) {
                                $number = $value[0];
                                if (is_numeric($number)) {
                                    if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                                        $number = "966" . substr($number, 1);
                                    } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                                        $number = "966" . $number;
                                    }

                                    $variables_message_text = $request['message'];
                                    for ($col = 'A'; $col < 'Z'; $col++) {
                                        if (isset($value[ord($col) - 65])) {
                                            $variables_message_text = str_replace("{{$col}}", $value[ord($col) - 65], $variables_message_text);
                                        }
                                    }
                                    $message_long = calc_message_length($variables_message_text, $enter_length);

                                }

                                //Get price by country
                                foreach ($countries as $key => $price) {
                                    preg_match("/^{$key}/", substr($number, 0, 5), $match);
                                    if (!empty($match)) {
                                        if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                                            $match_status = false;
                                            if (!empty($operators[$key])) {
                                                foreach ($operators[$key] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $cost[$number] = ($operator['price'] * $message_long);
                                                            $operator_id[$number] = $operator['id'];
                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                $message_price = $price * $message_long;
                                                $cost[] = ['to' => $number, 'price' => $message_price];
                                            }
                                            $num_variable_message[] = ['to' => $number, 'message' => $variables_message_text, 'length' => $message_long, 'cost' => $message_price];

                                            //Add country id
                                            $country_ids[$number] = $match[0];
                                            //Add to central array
                                            $number_arr[$number] = $number;
                                            // array_push($number_arr, $number);
                                            continue;
                                        }
                                    }
                                } //End foreach
                            } //End foreach
                            $rows_cnt += $this->real_time_send_limit;
                            $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, $this->real_time_send_limit);
                        } //End while loop
                    } //End for loop
                    // $this->session->set_userdata('excel_file_numbers', "");
                }
            } //End number foreach
            //Check number validation
            if (empty($number_arr)) {
                $this->message = ['type' => 'danger', 'text' => "We not get any send number2"];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            }
            $total_cost = round(array_sum(array_column($cost, 'price')));
        }


        //Check user balance

        if (!empty($request['repeation_period'])) { //Repeat message
            $total_cost = $total_cost * ($request['repeation_times'] + 1);
        }
        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            $this->message = ['type' => 'danger', 'text' => $this->lang->line('msg_error_insufficient_balance')];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }



        //Deduct balance
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            $this->message = ['type' => 'danger', 'text' => "Balance deduct failed"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }
        //Save to outbox
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'DIRECT',
            'user_id' => $this->session->userdata('user_logged_info')['id'],
            'text' => $request['message'],
            'count' => array_sum($total_numbers) > $this->real_time_send_limit ? array_sum($total_numbers) : count($num_variable_message),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $request['send_time_method'] == "LATER" ? $request['send_time'] : NULL,
            'repeation_period' => $request['send_time_method'] == "LATER" ? $request['repeation_period'] : "0",
            'repeation_times' => $request['send_time_method'] == "LATER" ? $request['repeation_times'] : 0,
            'variables_message' => ($request['sms_type'] == "VARIABLES" ? 1 : 0),
            'sender_name' => $request['sender_name'],
            'excel_file_numbers' => $this->session->userdata('excel_file_numbers'),
            'all_numbers' => implode(',', $all_numbers),
            //$request['all_numbers'],
            'number_index' => null,
            'encrypted' => $encrypt_number,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);

        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            $this->message = ['type' => 'danger', 'text' => "Insert failed to outbox"];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }

        //Send sms
        $output_message = [];
        //$sms_process = $this->_api_send_sms_multi($outbox_id, $auth_code, 0, array_column($num_variable_message, 'to'), $country_ids, $cost, $is_dlr, $num_variable_message);

        $send_sms = $this->pre_message_flush_by_auth_code($outbox_id, $auth_code, $encrypt_number, array_column($num_variable_message, 'to'), $country_ids, $cost, $num_variable_message, $operator_id);
        if ($send_sms['type'] == "success") { //Success
            $this->db->trans_commit();
            if ($advertising_words) {
                if ($this->site_settings['advertising_message'] == "SMS" || $this->site_settings['advertising_message'] == "BOTH") {
                    $this->send_telegram_message($this->lang->line('msg_info_messages_review_msg') . " |Sender: " . $request['sender_name'] . " |message_id: #" . $send_sms['data']['message_id']);

                    // $recive_numbers = explode(",", $this->site_settings['receiver_number_advertising']);
                    // foreach ($recive_numbers as $recive_number) {
                    //     $this->send_sms_admin(
                    //         $this->site_settings['system_sms_sender'],
                    //         $recive_number,
                    //         strip_tags($this->lang->line('msg_info_messages_review_msg')),
                    //         0,
                    //         null
                    //     );
                    // }

                }
            } elseif (empty($request['send_time']) && empty($outbox_param['variables_message'])) {//Regular message
                //Send cambian
                $res = $this->send_by_auth_code($send_sms['data']['message_id'], $auth_code);
            }
            if (!strpos($request['sender_name'], '-AD')) {
                if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                    $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram') . $this->session->userdata('user_logged_info')['username']);
                    // $this->send_sms_admin(
                    //     $this->site_settings['system_sms_sender'],
                    //     $this->site_settings['receiver_number'],
                    //     strip_tags($this->lang->line('msg_info_messages_overload')),
                    //     0,
                    //     array('{username}' => $this->session->userdata('user_logged_info')['username'])
                    // );
                }
            }

            $outbox_href = "<a href='" . site_url('user/outboxes') . "'>" . $this->lang->line('lbl_outbox') . "</a>";
            $sent_messages_href = "<a href='" . site_url('user/smsarchive') . "'>" . $this->lang->line('lbl_sent_messages') . "</a>";
            $output_message = array(
                'type' => 'success',
                'text' => str_replace("{sent_messeages_href}", $sent_messages_href, str_replace("{outbox_href}", $outbox_href, $this->lang->line('msg_success_outboxing')))
            );
        } else { //Error
            $this->db->trans_rollback();
            if (empty($send_sms['text'])) {
                $send_sms['text'] = "There is an unknown error please contact with administrator";
            }
            $output_message = $send_sms;
        }

        //Redirect page
        $sms_url = "user/sms";
        if ($request['sms_type'] == "VARIABLES")
            $sms_url .= "/variables";
        elseif ($request['sms_type'] == "ADS")
            $sms_url .= "/ads";
        elseif ($request['sms_type'] == "VOICE")
            $sms_url .= "/voicecall";
        elseif ($request['sms_type'] == "CALENDAR")
            $sms_url .= "/voicecall";

        $this->session->set_flashdata('message', $output_message);
        $this->session->set_userdata('excel_file_numbers', "");
        redirect($sms_url);
    }

    public function pre_message_flush_by_auth_code($outbox_id, $auth_code, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message = false, $operator_id)
    {
        //Get outbox info
        $outbox_info = $this->outbox->get_by_id_auth_code($outbox_id, $auth_code);
        if (empty($outbox_info)) {
            return ['type' => "danger", 'text' => "Outbox message not found"];
        }

        //Send limit
        $this->real_time_send_limit = $this->page_data['inner_page_data']['site_settings']['real_time_send_limit'];
        //Excel file
        $excel_file = './uploads/' . $outbox_info->excel_file_numbers;

        //Get user info
        $user_info = $this->user->get_by_id($outbox_info->user_id);
        if (empty($user_info)) {
            if (file_exists($excel_file)) {
                unlink($excel_file);
                delete_file_oss($excel_file);
            }
            return ['type' => "danger", 'text' => "User info not found"];
        }


        //Later message
        if (!empty($outbox_info->sending_datetime)) {
            $message_id = 0;
            for ($i = 0; $i < $outbox_info->repeation_times; $i++) {
                if ($outbox_info->repeation_period == "DAILY") {
                    $outbox_info->sending_datetime = date('Y-m-d h:i', strtotime($outbox_info->sending_datetime . ' + 1 day'));
                } elseif ($outbox_info->repeation_period == "WEEKLY") {
                    $outbox_info->sending_datetime = date('Y-m-d h:i', strtotime($outbox_info->sending_datetime . ' + 1 week'));
                } elseif ($outbox_info->repeation_period == "MONTHLY") {
                    $outbox_info->sending_datetime = date('Y-m-d h:i', strtotime($outbox_info->sending_datetime . ' + 1 month'));
                } elseif ($outbox_info->repeation_period == "YEARLY") {
                    $outbox_info->sending_datetime = date('Y-m-d h:i', strtotime($outbox_info->sending_datetime . ' + 1 year'));
                }

                //Insert to message tb
                $message_param = [
                    'user_id' => $outbox_info->user_id,
                    'channel' => $outbox_info->channel,
                    'text' => $outbox_info->text,
                    'creation_datetime' => date("Y-m-d H:i:s"),
                    'sending_datetime' => $outbox_info->sending_datetime,
                    'variables_message' => $outbox_info->variables_message,
                    'count' => $outbox_info->count,
                    'cost' => $outbox_info->cost,
                    'sender_name' => $outbox_info->sender_name,
                    'auth_code' => $outbox_info->auth_code,
                    'advertising' => $outbox_info->advertising
                ];

                $message_id = $this->message->insert_by_array($message_param);
                if (empty($message_id)) {
                    $this->db->trans_rollback();
                    // if (file_exists($excel_file)) {
                    //     //unlink($excel_file);
                    // }
                    return ['type' => "danger", 'text' => "Message insert failed"];
                }

                //Large number
                if ($outbox_info->count > $this->real_time_send_limit) {
                    //Update message id to outbox
                    $outbox_param = [
                        'id' => $outbox_info->id,
                        'message_id' => $message_id,
                        'updated_time' => date('Y-m-d H:i:s')
                    ];

                    $update_outbox = $this->outbox->update_status($outbox_param);
                    if (empty($update_outbox)) {
                        $this->db->trans_rollback();
                        return ['type' => "danger", 'text' => "Message id update failed to outbox"];
                    }
                } else {
                    //Insert to message details
                    $batch_insert = $this->messagedetails->insert_batch($message_id, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message, $operator_id);
                    if (empty($batch_insert)) {
                        $this->db->trans_rollback();
                        // if (file_exists($excel_file)) {
                        //     //unlink($excel_file);
                        // }
                        return ['type' => "danger", 'text' => "Message details insert failed"];
                    }

                    //Update message status to message
                    if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime) && $outbox_info->advertising == 0) {
                        $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $message_id));
                        if (empty($update_message)) {
                            $this->db->trans_rollback();
                            // if (file_exists($excel_file)) {
                            //     //unlink($excel_file);
                            // }
                            return ['type' => "danger", 'text' => "Message status update failed to message"];
                        }
                    }

                    //Delete outbox message
                    // if (file_exists($excel_file)) {
                    //     //unlink($excel_file);
                    // }
                    $outbox_info->delete();
                }
            }

            return ['type' => "success", 'text' => "Later message send successfully", 'data' => ['message_id' => $message_id]];
        } else { //Regular message
            //Insert to message tb
            $message_param = [
                'user_id' => $outbox_info->user_id,
                'channel' => $outbox_info->channel,
                'text' => $outbox_info->text,
                'creation_datetime' => date("Y-m-d H:i:s"),
                'sending_datetime' => $outbox_info->sending_datetime,
                'variables_message' => $outbox_info->variables_message,
                'count' => $outbox_info->count,
                'cost' => $outbox_info->cost,
                'sender_name' => $outbox_info->sender_name,
                'auth_code' => $outbox_info->auth_code,
                'advertising' => $outbox_info->advertising
            ];

            $message_id = $this->message->insert_by_array($message_param);

            if (empty($message_id)) {
                $this->db->trans_rollback();
                if (file_exists($excel_file)) {
                    unlink($excel_file);
                }
                return ['type' => "danger", 'text' => "Message insert failed"];
            }


            //Large number
            if ($outbox_info->count > $this->real_time_send_limit) {
                //Update message id to outbox
                $outbox_param = [
                    'id' => $outbox_info->id,
                    'message_id' => $message_id,
                    'updated_time' => date('Y-m-d H:i:s')
                ];

                $update_outbox = $this->outbox->update_status($outbox_param);
                if (empty($update_outbox)) {
                    $this->db->trans_rollback();
                    // if (file_exists($excel_file)) {
                    //     //unlink($excel_file);
                    // }
                    return ['type' => "danger", 'text' => "Message id update failed to outbox"];
                }
            } else { //Regular number

                //Insert to message details
                if (!empty($outbox_info->variables_message)) {
                    $batch_insert = $this->messagedetails->insert_batch_multi($message_id, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message);
                } else {
                    $batch_insert = $this->messagedetails->insert_batch($message_id, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message, $operator_id);
                }

                if (empty($batch_insert)) {
                    $this->db->trans_rollback();
                    if (file_exists($excel_file)) {
                        unlink($excel_file);
                        delete_file_oss($excel_file);
                    }
                    return ['type' => "danger", 'text' => "Message details insert failed"];
                }

                //Update message status to message
                if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime) && $outbox_info->advertising == 0) {
                    $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $message_id));
                    if (empty($update_message)) {
                        $this->db->trans_rollback();
                        if (file_exists($excel_file)) {
                            unlink($excel_file);
                            delete_file_oss($excel_file);
                        }
                        return ['type' => "danger", 'text' => "Message status update failed to message"];
                    }
                }

                //Delete outbox message
                if (file_exists($excel_file)) {
                    unlink($excel_file);
                    delete_file_oss($excel_file);
                }

                $outbox_info->delete();

            }

            return ['type' => "success", 'text' => "Message send successfully", 'data' => ['message_id' => $message_id]];
        }
    }

    public function send_outbox_sms($id, $auth_code)
    {

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();
        //Get outbox info
        $outbox_info = $this->outbox->get_by_id_auth_code($id, $auth_code);
        //Get user info
        $user_info = $this->user->get_by_id($outbox_info->user_id);

        if (empty($outbox_info)) {
            die(json_encode(['type' => "danger", 'text' => "Outbox message not found"]));
        }
        if ($outbox_info->proccess == "1") {

            die(json_encode(['type' => "danger", 'text' => "Outbox proccessing"]));
        }

        // start proccess
        $outbox_param = [
            'id' => $outbox_info->id,
            'proccess' => "1",
            'updated_time' => date('Y-m-d H:i:s'),
        ];
        $update_outbox = $this->outbox->update_status($outbox_param);
        $this->db->trans_commit();

        // $this->db->trans_begin();
        //Cron send limit
        $this->cron_send_limit = $this->page_data['inner_page_data']['site_settings']['cron_send_limit'];

        //Get message information
        $message_info = $this->message->get_message_by_id($outbox_info->message_id);

        if ($message_info['status'] == 'error') {
            die(json_encode(['type' => "danger", 'text' => "Message info not found"]));
        }

        $total_details_count = $message_info['data']['sent_cnt'];

        //Later sms & variable sms
        if (!empty($outbox_info->sending_datetime) || !empty($outbox_info->variables_message)) {
            $total_details_count = $this->messagedetails->get_count_by_message_id($outbox_info->message_id);
        }

        //File info
        $excel_file_path = empty($outbox_info->excel_file_numbers) ? NULL : "./uploads/{$outbox_info->excel_file_numbers}";
        //Delete outbox sms
        if ($outbox_info->count <= $total_details_count) {
            //Update message status to message
            if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime)) {
                $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $outbox_info->message_id));
                if (empty($update_message)) {
                    $this->db->trans_rollback();
                    return ['type' => "danger", 'text' => "Message status update failed to message"];
                }
            }

            //Delete outbox sms
            if ($outbox_info->delete()) {
                $this->db->trans_commit();
                if (!empty($outbox_info->excel_file_numbers)) {
                    if (file_exists($excel_file_path)) {
                        unlink($excel_file_path);
                    }
                    delete_file_oss($excel_file_path);
                }

                $outbox_param = [
                    'id' => $outbox_info->id,
                    'proccess' => "0",
                    'updated_time' => date('Y-m-d H:i:s'),
                ];
                $update_outbox = $this->outbox->update_status($outbox_param);
                $this->db->trans_commit();
                die(json_encode(['type' => "success", 'text' => "Outbox sms delete successfully"]));
            } else {
                $this->db->trans_rollback();
                die(json_encode(['type' => "danger", 'text' => "Outbox sms delete failed"]));
            }
        }


        $index = [];
        $number_index = json_decode($outbox_info->number_index, true);
        //Get country info
        // $get_countries = $this->country->get_active_countries();
        $get_countries = $this->country->get_active_countries_by_user_v2($outbox_info->user_id, $user_info->is_international);

        $countries = array_column($get_countries, 'price', 'id');
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');
        //Get operators info
        $operators = $this->operator->get_active_operators(array_keys($countries));
        //Message info
        $this->load->helper('message');
        $enter_length = $this->setting->get_value_by_name('enter_length');
        $message_long = calc_message_length($outbox_info->text, $enter_length);
        //Number info
        $all_numbers = array_filter(explode(',', $outbox_info->all_numbers));

        $all_numbers_default = $all_numbers;
        $all_numbers = array_slice($all_numbers_default, 0, $this->cron_send_limit);
        $number_arr = [];
        $cost = [];
        $country_ids = [];
        $num_variable_message = [];
        $operator_id = [];
        // file_put_contents('failCrone.log',"all_numbers_count:".count($all_numbers)."\n",FILE_APPEND); // debug
        $i = 0;


        foreach ($all_numbers as $num_key => $number) {

            $number = trim($number);
            if (is_numeric($number)) { //Number
                if (true) {
                    $rows_cnt = $number_index['number'];
                    $index['number'] = $rows_cnt + count($all_numbers);

                    //$i<= $this->cron_send_limit
                    if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                        $number = "966" . substr($number, 1);
                    } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                        $number = "966" . $number;
                    }


                    if (true) {//!in_array($number, $number_arr)
                        //Get price by country
                        foreach ($countries as $key => $price) {
                            preg_match("/^{$key}/", substr($number, 0, 5), $match);
                            if (!empty($match)) {
                                if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                                    $match_status = false;
                                    if (!empty($operators[$key])) {
                                        foreach ($operators[$key] as $operator) { //Operator foreach
                                            $codes = array_filter(explode(",", $operator['code']));
                                            foreach ($codes as $code) { //Code foreach
                                                preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                                if (!empty($match_operator)) {
                                                    $match_status = true;
                                                    $cost[$number] = ($operator['price'] * $message_long);
                                                    $operator_id[$number] = $operator['id'];
                                                    break;
                                                }
                                            } //End code foreach

                                            if ($match_status) {
                                                break;
                                            }
                                        } //End operator foreach
                                    } //End if operators

                                    if (!$match_status) {
                                        //Add to cost
                                        $cost[$number] = ($price * $message_long);
                                    }

                                    //Add country id
                                    $country_ids[$number] = $match[0];
                                    //Add to central array
                                    $number_arr[] = $number;
                                    // if(count($number_arr)==0){

                                    //     // array_push($number_arr,$number);
                                    // }else{
                                    //     $number_arr[]= $number;
                                    //     var_dump($number_arr);
                                    //     die();
                                    // }


                                    continue;
                                }
                            }
                        } //End foreach
                    }

                    //
                    //Remove number from outbox
                    unset($all_numbers_default[$num_key]);
                    $i++;
                }


            } elseif (!empty($number) && ($number[0] == "G")) { //Group

                $group_id = substr($number, 1);
                $group_contacts = $this->contact->get_group_contact($group_id, $user_info->id);
                //Group index
                $group_index = 0;


                if (!empty($number_index['G'])) {
                    $group_index = $number_index['G'];
                }
                if (!empty($group_contacts)) {
                    $group_contacts = array_slice(array_column($group_contacts, 'number'), $group_index, $this->cron_send_limit, true);
                    //Get last key
                    end($group_contacts);
                    $index['G'] = key($group_contacts) + 1;
                    //  $index['G'] = $group_index;

                    foreach ($group_contacts as $contacts) {
                        if (!in_array($contacts, $number_arr)) {
                            //Get price by country
                            foreach ($countries as $key => $price) {
                                preg_match("/^{$key}/", substr($contacts, 0, 5), $match);
                                if (!empty($match)) {
                                    if ((strlen($contacts) >= $country_min_number_count[$key]) && (strlen($contacts) <= $country_max_number_count[$key])) {
                                        $match_status = false;
                                        if (!empty($operators[$key])) {
                                            foreach ($operators[$key] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts, strlen($key), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $cost[$contacts] = ($operator['price'] * $message_long);
                                                        $operator_id[$contacts] = $operator['id'];
                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $cost[$contacts] = ($price * $message_long);
                                        }

                                        //Add country id
                                        $country_ids[$contacts] = $match[0];
                                        //Add to central array
                                        array_push($number_arr, $contacts);
                                        continue;
                                    }
                                }
                            } //End foreach
                        } //End if
                    } //End foreach
                } //End if
            } elseif ($number[0] == "A") { //Ads Contact
                $tag_id = substr($number, 1);
                $ad_contacts = $this->adcontact->get_add_contact($tag_id);
                //Advertisement index
                $ads_index = 0;
                if (!empty($number_index['A'])) {
                    $ads_index = $number_index['A'];
                }

                if (!empty($ad_contacts)) {
                    $ad_contacts = array_slice(array_column($ad_contacts, 'number'), $ads_index, $this->cron_send_limit, true);
                    //Get last key
                    end($ad_contacts);
                    $index['A'] = key($ad_contacts) + 1;
                    foreach ($ad_contacts as $contacts) {
                        if (!in_array($contacts, $number_arr)) {
                            //Get price by country
                            foreach ($countries as $key => $price) {
                                preg_match("/^{$key}/", substr($contacts, 0, 5), $match);
                                if (!empty($match)) {
                                    if ((strlen($contacts) >= $country_min_number_count[$key]) && (strlen($contacts) <= $country_max_number_count[$key])) {
                                        $match_status = false;
                                        if (!empty($operators[$key])) {
                                            foreach ($operators[$key] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts, strlen($key), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $cost[$contacts] = ($operator['price'] * $message_long);
                                                        $operator_id[$contacts] = $operator['id'];

                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $cost[$contacts] = ($price * $message_long);
                                        }

                                        //Add country id
                                        $country_ids[$contacts] = $match[0];
                                        //Add to central array
                                        array_push($number_arr, $contacts);
                                        continue;
                                    }
                                }
                            } //End foreach
                        } //End if
                    } //End foreach
                }
            } elseif ($number[0] == "S") { //Repeat message
                $repeat_numbers = $this->messagedetails->get_repeat_numbers(substr($outbox_info->all_numbers, 1));
                //Group index
                $repeat_index = 0;
                if (!empty($number_index['S'])) {
                    $repeat_index = $number_index['S'];
                }

                if (!empty($repeat_numbers)) {
                    $repeat_numbers = array_slice(array_column($repeat_numbers, 'number'), $repeat_index, $this->cron_send_limit, true);
                    //Get last key
                    end($repeat_numbers);
                    $index['S'] = key($repeat_numbers) + 1;
                    foreach ($repeat_numbers as $contacts) {
                        if (!in_array($contacts, $number_arr)) {
                            //Get price by country
                            foreach ($countries as $key => $price) {
                                preg_match("/^{$key}/", substr($contacts, 0, 5), $match);
                                if (!empty($match)) {
                                    if ((strlen($contacts) >= $country_min_number_count[$key]) && (strlen($contacts) <= $country_max_number_count[$key])) {
                                        $match_status = false;
                                        if (!empty($operators[$key])) {
                                            foreach ($operators[$key] as $operator) {
                                                //Operator foreach
                                                $codes = array_filter(explode(",", $operator['code']));
                                                foreach ($codes as $code) {
                                                    //Code foreach
                                                    preg_match("/^{$code}/", substr($contacts, strlen($key), 3), $match_operator);
                                                    if (!empty($match_operator)) {
                                                        $match_status = true;
                                                        $cost[$contacts] = ($operator['price'] * $message_long);
                                                        $operator_id[$contacts] = $operator['id'];
                                                        break;
                                                    }
                                                } //End code foreach

                                                if ($match_status) {
                                                    break;
                                                }
                                            } //End operator foreach
                                        } //End if operators

                                        if (!$match_status) {
                                            //Add to cost
                                            $cost[$contacts] = ($price * $message_long);
                                        }

                                        //Add country id
                                        $country_ids[$contacts] = $match[0];
                                        //Add to central array
                                        array_push($number_arr, $contacts);
                                        continue;
                                    }
                                }
                            } //End foreach
                        } //End if
                    } //End foreach
                }
            } elseif ($number[0] == "U") { //Unknown
                $this->message = ['type' => 'danger', 'text' => "Unknow type sms please contact with system admin"];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            } elseif ($number[0] == "F") { //Unknown
                $this->message = ['type' => 'danger', 'text' => "Unknow type sms please contact with system admin"];
                $this->session->set_flashdata('message', $this->message);
                redirect('user/sms');
            } elseif (($number == "excel_file") && (!empty($excel_file_path))) { //Excel file
                if (!get_file_oss($excel_file_path)) {
                    $this->db->trans_rollback();
                    die(json_encode(['type' => "danger", 'text' => "Outbox excel not found"]));
                }
                $this->load->library('simplexlsx');
                $xlsx = new SimpleXLSX($excel_file_path);


                //Excel index
                $rows_cnt = 0;
                if (!empty($number_index['EXCEL'])) {
                    $rows_cnt = $number_index['EXCEL'];
                }

                for ($sheetsCounter = 1; $sheetsCounter <= $xlsx->sheetsCount(); $sheetsCounter++) {
                    $rows = $xlsx->rowsFromTo($sheetsCounter, $rows_cnt, $this->cron_send_limit);
                    if (!empty($rows)) {
                        foreach ($rows as $value) {
                            $number = $value[0];
                            if (is_numeric($number)) {
                                if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                                    $number = "966" . substr($number, 1);
                                } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                                    $number = "966" . $number;
                                }

                                //Variable message
                                if ($outbox_info->variables_message) {
                                    $variables_message_text = $outbox_info->text;
                                    for ($col = 'A'; $col < 'Z'; $col++) {
                                        if (isset($value[ord($col) - 65])) {
                                            $variables_message_text = str_replace("{{$col}}", $value[ord($col) - 65], $variables_message_text);
                                        }
                                    }
                                    $message_long = calc_message_length($variables_message_text, $enter_length);
                                    // $num_variable_message[$number] = [
                                    //     'message' => $variables_message_text,
                                    //     'length' => $message_long,
                                    // ];
                                }
                            }
                            if (!isset($number_arr[$number]) || $outbox_info->variables_message == 1) {
                                //!in_array($number, $number_arr)


                                //Get price by country

                                foreach ($countries as $key => $price) {
                                    preg_match("/^{$key}/", substr($number, 0, 5), $match);
                                    // preg_match("/^966/", substr($number, 0, 5), $match2);
                                    // var_dump($countries).'|';
                                    // var_dump($match).'|'.$key;
                                    if (!empty($match)) {
                                        if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                                            $match_status = false;
                                            if (!empty($operators[$key])) {
                                                foreach ($operators[$key] as $operator) {
                                                    //Operator foreach
                                                    $codes = array_filter(explode(",", $operator['code']));
                                                    foreach ($codes as $code) {
                                                        //Code foreach
                                                        preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                                        if (!empty($match_operator)) {
                                                            $match_status = true;
                                                            $cost[$number] = ($operator['price'] * $message_long);
                                                            $operator_id[$number] = $operator['id'];

                                                            break;
                                                        }
                                                    } //End code foreach

                                                    if ($match_status) {
                                                        break;
                                                    }
                                                } //End operator foreach
                                            } //End if operators

                                            if (!$match_status) {
                                                //Add to cost
                                                $message_cost = ($price * $message_long);
                                                $cost[$number] = $message_cost;
                                                $num_variable_message[] = ['to' => $number, 'message' => $variables_message_text, 'length' => $message_long, 'cost' => $message_cost];
                                            }

                                            //Add country id
                                            $country_ids[$number] = $match[0];
                                            // $num_variable_message[$number] = [
                                            //     'message' => $variables_message_text,
                                            //     'length' => $message_long,
                                            // ];
                                            //Add to central array
                                            // array_push($number_arr, $number);
                                            $number_arr[$number] = $number;
                                            continue;
                                        }
                                    } else {
                                        unset($number_arr[$number]);
                                    }
                                } //End foreach
                            }
                        } //End foreach
                        //Get last key
                        end($rows);
                        $index['EXCEL'] = key($rows) + 1;
                    } //End if
                } //End for loop
            }
        } //End number foreach
        //Check number validation

        if (empty($number_arr)) {

            $outbox_param = [
                'id' => $outbox_info->id,
                'proccess' => "0",
                'updated_time' => date('Y-m-d H:i:s'),
            ];
            $update_outbox = $this->outbox->update_status($outbox_param);
            $this->db->trans_commit();
            if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime)) {
                $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $outbox_info->message_id));
            }
            die(json_encode(['type' => "danger", 'text' => "We not get any send number1"]));
        }

        if (!empty($outbox_info->json_numbers)) {
            $number_arr = array_diff($number_arr, json_decode($outbox_info->json_numbers, true));
        }

        //Insert to message details
        if ($outbox_info->variables_message) {
            $batch_insert = $this->messagedetails->insert_batch_multi($outbox_info->message_id, $outbox_info->encrypted, $number_arr, $country_ids, $cost, $num_variable_message);
        } else {
            $batch_insert = $this->messagedetails->insert_batch($outbox_info->message_id, $outbox_info->encrypted, $number_arr, $country_ids, $cost, $num_variable_message);
        }

        if (empty($batch_insert)) {
            $this->db->trans_rollback();
            die(json_encode(['type' => "danger", 'text' => "Message details insert failed"]));
        }

        //Update outbox number
        $outbox_param = [
            'id' => $outbox_info->id,
            'all_numbers' => implode(',', $all_numbers_default),
            'updated_time' => date('Y-m-d H:i:s'),
        ];

        $update_outbox = $this->outbox->update_status($outbox_param);
        //  var_dump(empty($update_outbox));
        //  die();
        if (empty($update_outbox)) {
            //  file_put_contents("send_outbox_sms.log","6 ----------"."\n",FILE_APPEND);
            //  file_put_contents("send_outbox_sms.log","All number update failed to outbox"."\n",FILE_APPEND);
            // $this->db->trans_rollback();
            // die(json_encode(['type' => "danger", 'text' => "All number update failed to outbox"]));
        }
        // motaz
        //Json number
        if (!empty($outbox_info->json_numbers)) {
            $number_arr = array_merge(json_decode($outbox_info->json_numbers, true), $number_arr);
        }
        //Update outbox number index
        $outbox_param = [
            'id' => $outbox_info->id,
            'number_index' => json_encode($index),
            'json_numbers' => json_encode(array_unique($number_arr)),
            'updated_time' => date('Y-m-d H:i:s'),
        ];
        $update_outbox = $this->outbox->update_status($outbox_param);
        if (empty($update_outbox)) {
            $this->db->trans_rollback();
            die(json_encode(['type' => "danger", 'text' => "Number index update failed to outbox"]));
        }

        // start proccess

        $outbox_param = [
            'id' => $outbox_info->id,
            'proccess' => "0",
            'updated_time' => date('Y-m-d H:i:s'),
        ];
        $update_outbox = $this->outbox->update_status($outbox_param);

        //Transaction complete
        $this->db->trans_commit();
        //Send cambian
        if (empty($outbox_info->sending_datetime) && empty($outbox_info->variables_message)) { //Regular message
            $this->pre_send_by_auth_code($outbox_info->message_id, $auth_code);
        }


        if (isset($index['number']) && $index['number'] % 20000 != 0) {

            $this->load->library('curl');
            $this->curl->_simple_call("get", site_url("user/sms/send_outbox_sms/{$id}/{$auth_code}"), array(), array("TIMEOUT" => 60));
        } elseif (isset($index['EXCEL']) && $index['EXCEL'] % 20000 != 0) {

            $this->load->library('curl');
            $this->curl->_simple_call("get", site_url("user/sms/send_outbox_sms/{$id}/{$auth_code}"), array(), array("TIMEOUT" => 60));
        } elseif ((isset($index['G']) && $index['G'] % 20000 != 0)) {
            echo "-----------s--------------:" . $index['G'];


            $this->load->library('curl');
            $this->curl->_simple_call("get", site_url("user/sms/send_outbox_sms/{$id}/{$auth_code}"), array(), array("TIMEOUT" => 60));
        }




        die(json_encode(['type' => "success", 'text' => "Message send successfully", 'data' => ['message_id' => $outbox_info->message_id]]));
    }


    public function pre_flush_by_auth_code($id, $auth_code, $encrypt_number = 0)
    {
        $this->load->library('curl');
        $send_sms = $this->curl->_simple_call("get", site_url("user/sms/flush_by_auth_code/{$id}/{$auth_code}/{$encrypt_number}/"), array(), array("TIMEOUT" => 0));
        die($send_sms);
    }

    public function flush_by_auth_code($id, $auth_code, $encrypt_number = 0)
    {
        $this->load->model('outbox');
        $entry = $this->outbox->get_by_id_auth_code($id, $auth_code);
        if (empty($entry)) {
            die(json_encode(['type' => "danger", 'text' => "Outbox message not found"]));
        }

        $this->load->model('user');
        $user = $this->user->get_by_id($entry->user_id);
        if (empty($user)) {
            $entry->delete();
            die(json_encode(['type' => "danger", 'text' => "User info not found"]));
        }

        $suffix = rand(1000000, 9999999);
        $excel_file_numbers = $entry->excel_file_numbers;
        $excel_file_path = empty($excel_file_numbers) ? "" : "./uploads/{$excel_file_numbers}";
        $this->load->model('messagedetails');
        $this->messagedetails->create_statistics_temp_table(
            $suffix,
            $entry->text,
            $entry->all_numbers,
            $entry->variables_message,
            $excel_file_path,
            (empty($entry->number_index) ? 0 : $entry->number_index)
        );

        $statistics = $this->messagedetails->get_statistics($suffix);
        $total_cnt = 0;
        $total_cost = 0;
        foreach ($statistics as $info) {
            if ($info['coverage_status'] == 1) {
                $total_cost += $info["cost"];
                $total_cnt += $info["cnt"];
            }
        } //End foreach

        if (($user->total_balance + $user->credit_limit - $user->spent_balance) < $total_cost) { //Check balance
            $entry->delete();
            $this->messagedetails->drop_statistics_temp_table($suffix);
            $this->message = array('type' => 'danger', 'text' => $this->lang->line('msg_error_insufficient_balance'));
            die(json_encode($this->message));
        } else {
            $this->load->model('message');
            $user->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
            $message_id = $this->message->insert_by_array(
                array(
                    'user_id' => $entry->user_id,
                    'channel' => $entry->channel,
                    'text' => $entry->text,
                    'creation_datetime' => date("Y-m-d H:i:s"),
                    'sending_datetime' => $entry->sending_datetime,
                    'variables_message' => $entry->variables_message,
                    'count' => $total_cnt,
                    'cost' => round($total_cost, 1),
                    'sender_name' => $entry->sender_name,
                    'auth_code' => $entry->auth_code
                )
            );

            //Save to temp table
            $this->messagedetails->save_statistics_temp_table($message_id, $suffix, $encrypt_number);

            //Later message
            if (!empty($entry->sending_datetime)) {
                if (!empty($entry->repeation_period) && !empty($entry->repeation_period)) {
                    for ($i = 0; $i < $entry->repeation_times; $i++) {
                        if ($entry->repeation_period == "DAILY") {
                            $entry->sending_datetime = date('Y-m-d h:i', strtotime($entry->sending_datetime . ' + 1 day'));
                        } elseif ($entry->repeation_period == "WEEKLY") {
                            $entry->sending_datetime = date('Y-m-d h:i', strtotime($entry->sending_datetime . ' + 1 week'));
                        } elseif ($entry->repeation_period == "MONTHLY") {
                            $entry->sending_datetime = date('Y-m-d h:i', strtotime($entry->sending_datetime . ' + 1 month'));
                        } elseif ($entry->repeation_period == "YEARLY") {
                            $entry->sending_datetime = date('Y-m-d h:i', strtotime($entry->sending_datetime . ' + 1 year'));
                        }

                        $user->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
                        $message_id = $this->message->insert_by_array(
                            array(
                                'user_id' => $entry->user_id,
                                'text' => $entry->text,
                                'creation_datetime' => date("Y-m-d H:i:s"),
                                'sending_datetime' => $entry->sending_datetime,
                                'variables_message' => $entry->variables_message,
                                'count' => $total_cnt,
                                'cost' => round($total_cost, 1),
                                'sender_name' => $entry->sender_name,
                                'auth_code' => $entry->auth_code
                            )
                        );

                        $this->messagedetails->save_statistics_temp_table($message_id, $suffix, $encrypt_number);
                    }
                }

                $this->messagedetails->drop_statistics_temp_table($suffix);
                $entry->delete();

                //Response data
                $data = [
                    'message_id' => $message_id
                ];

                die(json_encode(['type' => "success", 'text' => str_replace("{balance}", ($user->total_balance - $user->spent_balance), $this->lang->line('msg_success_sending')), 'data' => $data]));
            } else { //Regular message
                $this->messagedetails->drop_statistics_temp_table($suffix);
                $entry->delete();
                $this->send_by_auth_code($message_id, $entry->auth_code);
                //Response data
                $data = [
                    'message_id' => $message_id
                ];

                die(json_encode(['type' => "success", 'text' => str_replace("{balance}", ($user->total_balance - $user->spent_balance), $this->lang->line('msg_success_sending')), 'data' => $data]));
            }
        }
    }

    public function pre_send_by_auth_code($id, $auth_code)
    {
        $this->load->library('curl');
        $this->curl->_simple_call("get", site_url("user/sms/send_by_auth_code/{$id}/{$auth_code}/"), array(), array("TIMEOUT" => 0));
    }

    public function send_by_auth_code($id, $auth_code)
    {
        $this->load->model('message');
        $variable_send_limit = $this->page_data['inner_page_data']['site_settings']['variable_sms_send_limit'];
        $message = $this->message->get_by_id_auth_code($id, $auth_code);

        if (!empty($message)) {
            $this->load->model('gateway');
            $res = $this->gateway->send_campaign($id, $message->variables_message, $this->site_settings['default_gateway'], 0, $variable_send_limit);
        }

    }

    public function server_datetime()
    {
        $offsettime = $this->site_settings['offset_time'];
        echo $this->lang->line('lbl_server_time') . "<span dir='ltr'>" . date("Y-m-d H:i:s", strtotime("now -$offsettime minutes")) . "</span>";
    }

    public function server_time()
    {
        $offsettime = $this->site_settings['offset_time'];
        return date("H:i:s", strtotime("now -$offsettime minutes"));
    }

    public function prof_flag($str)
    {
        global $prof_timing, $prof_names;
        $prof_timing[] = microtime(true);
        $prof_names[] = $str;
    }

    public function prof_print()
    {
        global $prof_timing, $prof_names;
        $size = count($prof_timing);
        for ($i = 0; $i < $size - 1; $i++) {
            echo "<b>{$prof_names[$i]}</b><br>";
            echo sprintf("&nbsp;&nbsp;&nbsp;%f<br>", $prof_timing[$i + 1] - $prof_timing[$i]);
        }
        echo "<b>{$prof_names[$size - 1]}</b><br>";
    }








    public function testbadwords()
    {

        $message = $this->input->post_get('message');
        $user_id = $this->session->userdata('user_logged_info')['id'];
        $this->load->model('setting');

        $message_words = explode(" ", $message);

        $bad_words = explode(",", $this->site_settings['bad_words']);
        foreach ($message_words as $word) {
            if (in_array($word, $bad_words)) {
                /* $this->db->insert(
                 'badwords_log',
                 array("user_id" => $user_id, "badword" => $word));*/
                dd('bad words');
            }
        }
        dd("good word");
    }

    private function createBackgroundJob($request, $user_info, $data)
    {
        try {
            // Prepare request data for background processing
            $request['excel_file_numbers'] = $this->session->userdata('excel_file_numbers');

            // Insert job into sms_processing_jobs table
            $job_data = [
                'user_id' => $user_info->id,
                'request_data' => json_encode($request),
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'progress' => 0,
                'progress_message' => 'Job created, waiting to be processed'
            ];

            $this->db->insert('sms_processing_jobs', $job_data);
            $job_id = $this->db->insert_id();

            if ($job_id) {
                // Load Redis queue library and push job
                $this->load->library('RedisQueue');
                $this->redisqueue->push('SmsProcessing', ['job_id' => $job_id]);

                // Show success message to user
                return ['type' => 'success', 'text' => 'تم إنشاء مهمة معالجة في الخلفية لملف Excel الكبير. ستتم معالجة الطلب قريباً.', 'excel_file' => true, 'data' => ['job_id' => $job_id]];
            } else {
                throw new Exception("فشل في إنشاء مهمة المعالجة");
            }
        } catch (Exception $e) {
            // Log the error
            log_message('error', 'Failed to create background job: ' . $e->getMessage());

            // Notify admin about job creation failure
            $this->load->service('adminnotificationservice');
            $this->adminnotificationservice->notifyAdminForAdvertising();

            // Show error message to user
            $this->message = [
                'type' => 'danger',
                'text' => 'فشل في إنشاء مهمة المعالجة: ' . $e->getMessage()
            ];
            $this->session->set_flashdata('message', $this->message);
            redirect('user/sms');
        }
    }


}
