<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Apilog extends MY_Model
{

    var $url;
    var $parameters;
    var $ip;
    var $date;
    var $status;

    public function __construct()
    {
        parent::__construct();
        $this->load->library('logger');
        $this->table_name = $this->table_names['api_log'];
    }

    public function to_array()
    {
        return array(
            'id' => $this->id,
            'url' => $this->url,
            'parameters' => $this->parameters,
            'ip' => $this->ip,
            'date' => $this->date,
            'status' => $this->status
        );
    }

    // Read

    public function load_data($params)
    {
        $this->default_columns = array('id', 'url', 'parameters', 'ip', 'date');
        $this->search_columns = array('ip');
        $this->sort_columns = array('id', 'url', 'ip', 'date');
        return parent::load_data($params);
    }

    public function get_row($row)
    {
        $res = array(
            'id' => $row->id,
            'url' => $row->url,
            'parameters' => $row->parameters,
            'ip' => $row->ip,
            'date' => $row->date,
            'status' => $row->status
        );
        return $res;
    }

    public function insert_by_array($obj_data)
    {
        return parent::insert_by_array($obj_data);
    }

    public function update_by_array($obj_data, $conditions)
    {
        $this->logger->info('response_api_log', $obj_data);
        unset($obj_data['text']);
        return parent::update_by_array($obj_data,$conditions);
    }

}