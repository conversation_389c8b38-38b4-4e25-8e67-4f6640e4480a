<link href="<?php echo base_url('/assets/metronic'); ?>/global/plugins/autocomplete/autoSuggest.css" type="text/css" rel="stylesheet" />
<link href="<?php echo base_url('/assets/metronic'); ?>/global/plugins/jstree/dist/themes/default/style.min.css" type="text/css" rel="stylesheet" />
<link href="<?php echo base_url('/assets/metronic'); ?>/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" type="text/css" rel="stylesheet" />
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/autocomplete/jquery.autoSuggest.js"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/jstree/dist/jstree.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/bootstrap-datetimepicker/locales/bootstrap-datetimepicker.ar.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/jquery-validation/js/additional-methods.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic'); ?>/global/plugins/jquery.form.js" type="text/javascript"></script>
<script language="javascript">
   function searchJstree(query) {
       // $('#tree_groups').jstree(true).search(query);
        $('#jstree').jstree('search', query);
        // Hide all nodes
        $('#tree_groups').find('.jstree-node').hide();

        // Show only matching nodes
        $('#tree_groups').find('.jstree-node').filter(function() {
            return $(this).text().toLowerCase().includes(query);
        }).show();

    }

    function toUnicodeEscape(str) {
    return Array.from(str).map(function(char) {
        var unicode = char.codePointAt(0).toString(16).toUpperCase();
        while (unicode.length < 4) {
            unicode = '0' + unicode;
        }
        return '\\u' + unicode;
    }).join('');
}

    $(document).ready(function() {

        // Listen for input changes in search input field
        $('#search_input').on('input', function() {
            var search_query = $(this).val().toLowerCase();

            // Perform client-side search
           searchJstree(search_query);
        });
        senders = new Array;
        <?php
        foreach ($senders as $key => $sender) {
            if ($sender['default']) {
                echo "$('#sender_name').val('{$sender['name']}');";
            }
            echo "senders[$key]='" . $sender['name'] . "';\n";
        }
        ?>
        senders.push("<?php echo $this->site_settings['default_sender']; ?>");

        maxMsgCnt = <?php echo $this->site_settings['max_message_count']; ?>;

        IsEnglish = function(inputString) {
            if (inputString.match('^[0-9A-Za-z" ,.!;:£?$&+=/_)(@#%*{}<>\'\n\r|\^\-]+$')) {
                return true;
            } else {
                return false;
            }
        }

        calculateChars = function(message, char_cnt, msg_cnt, cons_char_cnt, maxMsgCnt) {
            var maxArabicChar = 70;
            var maxEnglishChar = 160;
            var numOfCharsTyped = 0;
            var numOfTechincalMsgs = 0;
            var max_message_count = maxMsgCnt;
            numOfCharsTyped = $("#" + message).val().length;
            <?php if ($site_settings['enter_length'] > 1) { ?>
                numOfCharsTyped += $("#" + message).val().split("\n").length - 1;
            <?php } ?>
            var isEnglish = IsEnglish($("#" + message).val());
            if (isEnglish) {
                $("#" + message).css("direction", "ltr");
                if (numOfCharsTyped <= 160)
                    maxEnglishChar = 160;
                else
                    maxEnglishChar = 153;
                if (max_message_count == 1)
                    maxEnglishChar = 160;
                if (max_message_count == 1 && numOfCharsTyped > 160)
                    numOfCharsTyped = 160;
                numOfTechincalMsgs = Math.ceil(numOfCharsTyped / maxEnglishChar);
                if (numOfCharsTyped >= (maxEnglishChar * max_message_count)) {
                    $("#" + message).val($("#" + message).val().substring(0, maxEnglishChar * max_message_count));
                    numOfCharsTyped = maxEnglishChar * max_message_count;
                    numOfTechincalMsgs = Math.ceil(numOfCharsTyped / maxEnglishChar);
                }
                $("#" + char_cnt).html((numOfTechincalMsgs * maxEnglishChar) - numOfCharsTyped);
                $("#" + cons_char_cnt).html(numOfCharsTyped);
            } else {
                $("#" + message).css("direction", "rtl");
                if (numOfCharsTyped <= 70)
                    maxArabicChar = 70;
                else
                    maxArabicChar = 67;
                if (max_message_count == 1)
                    maxArabicChar = 70;
                if (max_message_count == 1 && numOfCharsTyped > 70)
                    numOfCharsTyped = 70;
                numOfTechincalMsgs = Math.ceil(numOfCharsTyped / maxArabicChar);
                if (numOfCharsTyped >= (maxArabicChar * max_message_count)) {
                    $("#" + message).val($("#" + message).val().substring(0, maxArabicChar * max_message_count));
                    numOfCharsTyped = maxArabicChar * max_message_count;
                    numOfTechincalMsgs = Math.ceil(numOfCharsTyped / maxArabicChar);
                }
                $("#" + char_cnt).html((numOfTechincalMsgs * maxArabicChar) - numOfCharsTyped);
                $("#" + cons_char_cnt).html(numOfCharsTyped);
            }
            $("#" + msg_cnt).html(numOfTechincalMsgs);
        }

        afterSuccess = function(data) {
            if (data.response == "error") {
                bootbox.alert(data.text);
            } else if (data.sms_type == "VARIABLES") {
                $("#div_xlsx_data").html(data.text);
            }
            if (!$('#as-selections-1').length) {
                $("#as-values-1").val("excel_file");
            } else {
                $.fn.autoSuggest.remove_item_by_tree("excel_file");
                $.fn.autoSuggest.add_item_from_tree('<?php echo $this->lang->line("lbl_excel_file"); ?>', "excel_file");

            }


            $("#excel_file_progress_container").addClass("hidden");
            $("#excel_file_progress_container").css('width', '10%');
        }

        beforeSubmit = function() {
            if (!($("#exc_upload #excel_file").val()) ||
                $("#exc_upload #excel_file").val().indexOf("xls", $("#exc_upload #excel_file").val().length - 3) != -1 ||
                $("#exc_upload #excel_file").val().indexOf("xlsx", $("#exc_upload #excel_file").val().length - 4) != -1 ||
                $("#exc_upload #excel_file").val().indexOf("XLSX", $("#exc_upload #excel_file").val().length - 4) != -1
            ) {
                $("#excel_file_progress_container").removeClass("hidden");
                $("#div_xlsx_data").html();
                return true;
            } else {
                bootbox.alert({
                    title: '<?php echo $this->lang->line('lbl_alert'); ?>',
                    message: '<?php echo $this->lang->line('msg_error_excel'); ?>',
                    buttons: {
                        'ok': {
                            label: globalGoButton,
                            className: 'btn-danger pull-left'
                        }
                    },
                    callback: function(result) {}
                });
                return false;
            }
        }

        OnProgress = function(event, position, total, percentComplete) {
            $("#excel_file_progress_container").css('width', percentComplete + '%');
        }

        add_var = function(tag) {
            item = document.getElementById("message");
            prefix = item.value.substring(0, item.selectionStart);
            suffix = item.value.substring(item.selectionStart, item.value.length);
            item.value = prefix + "{" + tag + "}" + suffix;
        }

        clear_numbers = function() {
            $('#tree_groups').jstree("deselect_all");
            $("#div_all_numbers ul").find("li.as-selection-item").each(function() {
                $.fn.autoSuggest.remove_item_by_tree(this.id.replace("as-selection-", ""));
            });
            $("#send_form_id #as-values-1").val('');
        }
        clear_numbers_input = function() {
            $("#div_all_numbers").html("");
            $textarea = $(document.createElement('textarea')).attr({
                name: 'as-values-1',
                id: "as-values-1",
                class: "form-control",
                cols: "30",
                rows: "10"
            });
            $("#div_all_numbers").html($textarea);

        }
        onclick_favorit = function(text = '') {
            var selectedValue = $("#favorites_dropown").val();
            $('#message').val(selectedValue);
            calculateChars("message", "char_cnt", "msg_cnt", "cons_char_cnt", maxMsgCnt);
        }

        $("#send_form_id").validate({
            errorElement: 'span',
            errorClass: 'help-block',
            focusInvalid: false,
            ignore: "",
            submitHandler: function(form) {
                App.blockUI({
                    boxed: true,
                    calculator: true
                });
                const message = $("#send_form_id #message").val()
    .replace(/%/g, '[percent]')
    .replace(/@/g, '[at]')
    .replace(/:/g, '[colon]')
    .replace(/\?/g, '[question]')
    .replace(/=/g, '[equals]')
    .replace(/&/g, '[amp]');
                $.ajax({
                    type: "POST",
                    url: global_site_url + "/user/sms/statistics",
                    data: {
                        "all_numbers": $("#as-values-1").val(),
                        "sender_name": $("#sender_name").val(),
                        "message": encodeURIComponent(message),
                        "send_time_method": $('#send_form_id input[name=send_time_method]:radio:checked').val(),
                        "send_time": $("#send_form_id #send_time").val(),
                        "sms_type": $("#send_form_id #sms_type").val(),
                        "number_index": $("#send_form_id input:radio[name='number_index']:checked").val(),
                        "repeation_period": $("#send_form_id #repeation_period").val(),
                        "repeation_times": $("#send_form_id #repeation_times").val()
                    },
                    dataType: "html",
                    success: function(data) {
                          App.unblockUI();
                        if (typeof data === 'string') {
                            try {
                                var jsonData = JSON.parse(data);
                                if (jsonData.excel_file) {
                                    bootbox.alert({
                                        message: jsonData.text,
                                        className: 'bb-alternate-modal',
                                        callback: function() {
                                           clear_numbers_input();
                                           $("#sender_name").val("");
                                           $("#as-values-1").val("");
                                           $("#message").val("");
                                        }
                                    });
                                }
                            } catch(e) {
                                console.log(e);
                              
                                $("#modal_body_statistics").html(data);
                                $("#modal_statistics").modal();
                            }
                        } 
                       
                    },
                    complete: function() {}
                });
            },
            rules: {
                contact_error: {
                    requiredUNLESSexcel: true
                },
                send_time: {
                    requiredIFlater: true
                },
                message: {
                    required: true
                },
                sender_name: {
                    required: true
                },
                repeation_times: {
                    number: true
                }
            },
            errorPlacement: function(error, element) {
                if (element.parent(".input-group").size() > 0) {
                    error.insertAfter(element.parent(".input-group"));
                } else if (element.attr("data-error-container")) {
                    error.appendTo(element.attr("data-error-container"));
                } else if (element.parents('.radio-list').size() > 0) {
                    error.appendTo(element.parents('.radio-list').attr("data-error-container"));
                } else if (element.parents('.radio-inline').size() > 0) {
                    error.appendTo(element.parents('.radio-inline').attr("data-error-container"));
                } else if (element.parents('.checkbox-list').size() > 0) {
                    error.appendTo(element.parents('.checkbox-list').attr("data-error-container"));
                } else if (element.parents('.checkbox-inline').size() > 0) {
                    error.appendTo(element.parents('.checkbox-inline').attr("data-error-container"));
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function(element) {
                $(element).closest('.form-group').removeClass("has-success").addClass("has-error");
            },
            success: function(element) {
                $(element).closest('.form-group').removeClass("has-error").addClass("has-success").find("label.error").remove();
            }
        });
        $.validator.addMethod("requiredIFlater", function() {
            if ($('#send_form_id input[name=send_time_method]:radio:checked').val() == "LATER" && !$("#send_form_id #send_time").val()) {
                return false;
            } else {
                return true;
            }
        }, "");
        $.validator.addMethod("requiredUNLESSexcel", function() {
            if (!$("#send_form_id #excel_file").val() && !$("#send_form_id #as-values-1").val()) {
                return false;
            } else {
                return true;
            }
        }, "");

        $('#send_later').click(function() {
            $('#sendLaterContainer').removeClass("hidden");
            refreshId = setInterval(function() {
                $('#server-time').load(global_site_url + '/user/sms/server_datetime');
            }, 1000);
        });
        $('#send_now').click(function() {
            $('#sendLaterContainer').addClass("hidden");
            clearInterval(refreshId);
        });

        $(".form_datetime").datetimepicker({
            autoclose: true,
            isRTL: App.isRTL(),
            format: "yyyy-mm-dd hh:ii",
            language: "<?php echo $this->view_settings['lang']; ?>"
        });

        $("#message").keyup(function() {
            calculateChars("message", "char_cnt", "msg_cnt", "cons_char_cnt", maxMsgCnt)
        });

        <?php if ($this->session->userdata('user_logged_info')['unlimited_senders']) { ?>
            $("#sender_name").autocomplete({
                source: senders,
                minLength: 0
            });
            $("#sender_name").focus(function() {
                $(this).autocomplete('search', '');
            });
        <?php } ?>

        $('#excel_file_add').click(function() {
            $('#excel_file').click();
        });
        $('#excel_file').change(function() {
            if ($('#excel_file').val()) {
                $("#excel_file_label").removeClass("hidden");
                $("#excel_file_upload").removeClass("hidden");
            } else {
                $("#excel_file_label").addClass("hidden");
                $("#excel_file_upload").addClass("hidden");
            }
            $("#excel_file_label").html($("#excel_file").val());
        });
        $('#excel_file_upload').click(function() {
            var options = {
                beforeSubmit: beforeSubmit,
                success: afterSuccess,
                uploadProgress: OnProgress,
                dataType: "json",
                resetForm: true
            };
            $('#exc_upload').ajaxSubmit(options);
        });

        $("#contact").autoSuggest(
            global_site_url + "/user/sms/phonebook_autocomplete", {
                minChars: 3,
                matchCase: false,
                selectedItemProp: "name",
                selectedValuesProp: "value",
                searchObjProps: "name,value",
                selectionAdded: function() {
                    $(".as-results").css('display', 'none');
                },
                startText: ""
            }
        );

        $('#tree_groups').jstree({
            'plugins': ["types", "checkbox"],
            'core': {
                "themes": {
                    "responsive": false
                },
                "multiple": true
            },
            "types": {
                "default": {
                    "icon": "fa fa-folder icon-state-warning icon-lg"
                },
                "file": {
                    "icon": "fa fa-file icon-state-warning icon-lg"
                },
                "number": {
                    "icon": "fa fa-phone icon-state-success icon-lg"
                },
            },
          // 
            "checkbox": {
                "three_state": false
            },
            // 'plugins': ['search', 'checkbox'],
          


        }).on('select_node.jstree', function(e, data) {
            if (!$('#as-selections-1').length) {
                $form = $("#send_form_id");
                $txtarea = $form.find("#as-values-1");
                textbox = $(document.createElement('input')).attr({
                    'name': 'name',
                    id: "contact",
                    calss: "form-control",
                    style: "width:200%"
                });
                $txtarea.replaceWith(textbox);

                $("#contact").autoSuggest(
                    global_site_url + "/user/sms/phonebook_autocomplete", {
                        minChars: 3,
                        matchCase: false,
                        selectedItemProp: "name",
                        selectedValuesProp: "value",
                        searchObjProps: "name,value",
                        selectionAdded: function() {
                            $(".as-results").css('display', 'none');
                        },
                        startText: ""
                    }
                );

            }


            if (data.node.type == "number") {
                $.fn.autoSuggest.add_item_from_tree(data.node.text, data.node.id.split('-')[1]);
            } else {
                $.fn.autoSuggest.add_item_from_tree(data.node.text, "G" + data.node.id);
            }
        }).on('deselect_node.jstree', function(e, data) {
            if (data.node.type == "number") {
                $.fn.autoSuggest.remove_item_by_tree(data.node.id);
            } else {
                $.fn.autoSuggest.remove_item_by_tree("G" + data.node.id);
            }
        });

        $('#tree_tags').jstree({
            'plugins': ["types", "checkbox"],
            'core': {
                "themes": {
                    "responsive": false
                },
                "multiple": true
            },
            "types": {
                "default": {
                    "icon": "fa fa-folder icon-state-warning icon-lg"
                },
                "file": {
                    "icon": "fa fa-file icon-state-warning icon-lg"
                }
            },
            "checkbox": {
                "three_state": false
            }
        }).on('select_node.jstree', function(e, data) {
            $.fn.autoSuggest.add_item_from_tree(data.node.text, "A" + data.node.id);
        }).on('deselect_node.jstree', function(e, data) {
            $.fn.autoSuggest.remove_item_by_tree("A" + data.node.id);
        });

        $(window).keydown(function(event) {
            // if (event.keyCode == 13 && !$("#message").is(":focus")) {
            //     event.preventDefault();
            //     return false;
            // }
        });

        <?php if (!empty($type) && ($type == "NORMAL")) { ?>
            $(".li_user_index_sms_item").addClass("active");
        <?php } elseif (!empty($type) && ($type == "VARIABLES")) { ?>
            $(".li_user_index_sms_variables_item").addClass("active");
        <?php } elseif (!empty($type) && ($type == "ADS")) { ?>
            $(".li_user_index_sms_ads_item").addClass("active");
        <?php } ?>

        <?php if (!empty($resent_message)) { ?>
            $.fn.autoSuggest.add_item_from_tree("<?php echo $this->lang->line('lbl_repeated_message'); ?>", "S<?php echo $resent_message['id']; ?>");
        <?php } ?>
    });
</script>
<div class="page-content-container" dir="<?php echo $view_settings['dir']; ?>">
    <div class="page-content-row">
        <div class="page-content-col">
            <?php
            if (empty($message)) {
                $message = $this->session->flashdata('message');
            }
            ?>
            <?php if (isset($message)) { ?>
                <div class="col-lg-12 alert alert-<?php echo $message['type']; ?>"><?php echo $message['text']; ?></div>
            <?php } ?>
            <div class="row">
                <div class="col-md-12">
                    <div id="entries_container" class="portlet light portlet-fit portlet-datatable bordered">
                        <div class="portlet-title">
                            <div class="caption">
                                <span class="sbold uppercase" style="color:#5b9bd1;"> <i class="fa fa-envelope"></i>
                                    <?php echo $this->lang->line('lbl_the_sending'); ?>/
                                    <?php
                                    if (!empty($type) && ($type == "NORMAL")) {
                                        echo "<span class='sbold uppercase' style='color:#5b9bd1;'> <i class='fa fa-envelope-o'></i> " . $this->lang->line('lbl_new_message');
                                    } elseif (!empty($type) && ($type == "VARIABLES")) {
                                        echo "<span class='sbold uppercase' style='color:#5b9bd1;'> <i class='fa fa-comments-o'></i> " . $this->lang->line('lbl_new_variables_message');
                                    } elseif (!empty($type) && ($type == "ADS")) {
                                        echo "<span class='sbold uppercase' style='color:#5b9bd1;'> <i class='fa fa-table'></i> " . $this->lang->line('lbl_new_ads_message');
                                    }
                                    ?></span>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="table-container" id="entries_container">
                                <form class="form-horizontal" method="post" type="post" id="send_form_id">
                                    <div class="form-body">
                                        <?php if (!empty($type) && ($type == "NORMAL")) { ?>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="portlet box green">
                                                        <div class="portlet-title">
                                                            <div class="caption"><i class="fa fa-reorder"></i><?php echo $this->lang->line('lbl_phonebook'); ?></div>
                                                        </div>
                                                        <div class="portlet-body">
                                                            <input type="text" id="search_input" placeholder="Search" class="form-control">

                                                            <div class="scroller" style="height:200px">
                                                                <div id="tree_groups">
                                                                    <ul>
                                                                        <li id="0"><?php echo $this->lang->line('lbl_contacts') . " - " . $unclassified_cnt; ?></li>
                                                                        <?php foreach ($contact_groups as $contact_group) { ?>
                                                                            <li id="<?php echo $contact_group['id']; ?>"><?php echo $contact_group['name'] . " - " . $contact_group["count"]; ?>
                                                                                <?php if (!empty($contact_group['groups'])) { ?>
                                                                                    <ul>
                                                                                        <?php foreach ($contact_group['groups'] as $contact_sub_group) { ?>
                                                                                            <li id="<?php echo $contact_sub_group['id']; ?>"><?php echo $contact_sub_group['name'] . " - " . $contact_sub_group["count"]; ?>
                                                                                                <?php if (!empty($contact_sub_group['numbers'])) { ?>

                                                                                                    <ul>
                                                                                                        <?php foreach ($contact_sub_group['numbers'] as $number) { ?>
                                                                                                            <li data-jstree='{ "type" : "number" }' id="<?php echo $number->group_id . '-' . $number->number ?>"><?php echo $number->name  ?></li>
                                                                                                        <?php } ?>
                                                                                                    </ul>

                                                                                                <?php } ?>
                                                                                            </li>
                                                                                        <?php } ?>
                                                                                    </ul>
                                                                                <?php } ?>
                                                                                <?php if (!empty($contact_group['numbers'])) { ?>
                                                                                    <ul>
                                                                                        <?php foreach ($contact_group['numbers'] as $number) { ?>
                                                                                            <li data-jstree='{ "type" : "number" }' id="<?php echo $number->group_id . '-' . $number->number ?>"><?php echo $number->name ?></li>
                                                                                        <?php } ?>
                                                                                    </ul>

                                                                                <?php } ?>
                                                                            </li>
                                                                        <?php } ?>
                                                                        <?php if (!empty($granted_contact_groups)) { ?>
                                                                            <?php foreach ($granted_contact_groups as $granted_contact_group) { ?>
                                                                                <li id="<?php echo $granted_contact_group['id']; ?>"><?php echo $granted_contact_group['name'] . " - " . $granted_contact_group["count"]; ?>
                                                                                <?php } ?>
                                                                            <?php } ?>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="portlet box green">
                                                        <div class="portlet-title">
                                                            <div class="caption"><i class="fa fa-reorder"></i><?php echo $this->lang->line('lbl_import'); ?></div>
                                                            <div class="actions">
                                                                <div class="btn-group btn-group-devided" data-toggle="buttons">
                                                                    <span id="excel_file_add" class="dt-button buttons-collection buttons-colvis btn blue btn">
                                                                        <i class="fa fa-plus"></i>
                                                                        <?php echo $this->lang->line('act_chose_file'); ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="portlet-body">
                                                            <div class="scroller" style="height:200px">
                                                                <?php echo $this->lang->line('msg_info_received_normal_sms_numbers_import'); ?>
                                                                <br /><br />
                                                                <span class="btn green hidden" id="excel_file_label"></span>
                                                                <span class="btn blue hidden" id="excel_file_upload"><?php echo $this->lang->line('act_upload_file'); ?></span>
                                                                <br /><br />
                                                                <div id="excel_file_progress_container" class="progress progress-striped active hidden">
                                                                    <div id="excel_file_progress" class="progress-bar progress-bar-success" role="progressbar" style="width: 10%"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } elseif (!empty($type) && ($type == "VARIABLES")) { ?>
                                            <div class="row">
                                                <label class="control-label col-md-2"><?php echo $this->lang->line('fld_numbers'); ?></label>
                                                <div class="col-md-7">
                                                    <div class="portlet box green">
                                                        <div class="portlet-title">
                                                            <div class="caption"><i class="fa fa-reorder"></i><?php echo $this->lang->line('act_import'); ?></div>
                                                            <div class="actions">
                                                                <div class="btn-group btn-group-devided" data-toggle="buttons">
                                                                    <span id="excel_file_add" class="dt-button buttons-collection buttons-colvis btn blue btn">
                                                                        <i class="fa fa-plus"></i>
                                                                        <?php echo $this->lang->line('act_chose_file'); ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="portlet-body">
                                                            <div style="overflow-x:hidden;">
                                                                <?php echo $this->lang->line('msg_info_received_variables_sms_numbers_import'); ?>
                                                                <br /><br />
                                                                <span class="btn green hidden" id="excel_file_label"></span>
                                                                <span class="btn blue hidden" id="excel_file_upload"><?php echo $this->lang->line('act_upload_file'); ?></span>
                                                                <br /><br />
                                                                <div id="excel_file_progress_container" class="progress progress-striped active hidden">
                                                                    <div id="excel_file_progress" class="progress-bar progress-bar-success" role="progressbar" style="width: 10%"></div>
                                                                </div>
                                                                <div id="div_xlsx_data"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } elseif (!empty($type) && ($type == "ADS")) { ?>

                                            <div class="row">
                                                <label class="control-label col-md-2"><?php echo $this->lang->line('lbl_numbers'); ?></label>
                                                <div class="col-md-7">
                                                    <div class="portlet box green">
                                                        <div class="portlet-title">
                                                            <div class="caption"><i class="fa fa-reorder"></i><?php echo $this->lang->line('lbl_tags'); ?></div>
                                                        </div>
                                                        <div class="portlet-body">
                                                            <div>
                                                                <div id="tree_tags">
                                                                    <ul>
                                                                        <?php foreach ($tags as $tag) { ?>
                                                                            <li data-jstree='{"disabled":true}' id="<?php echo $tag['id']; ?>">
                                                                                <?php echo $tag["name_{$view_settings['lang']}"]; ?>
                                                                                <!--Refresh button-->
                                                                                <span class="glyphicon glyphicon-refresh" onclick="getTotalCount(<?php echo $tag['id']; ?>)"></span>

                                                                                <?php if (!empty($tag['subtags'])) { ?>
                                                                                    <ul>
                                                                                        <?php foreach ($tag['subtags'] as $subtag) { ?>
                                                                                            <li id="<?php echo $subtag['id']; ?>"><?php echo $subtag["name_{$view_settings['lang']}"]; ?> <span style="color: red;" id="totalCount_<?php echo $subtag['id']; ?>"></span></li>
                                                                                        <?php } ?>
                                                                                    </ul>
                                                                                <?php } ?>
                                                                            </li>
                                                                        <?php } ?>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><?php echo $this->lang->line('fld_sender_name'); ?></label>
                                            <div class="col-md-7">
                                                <?php if ($this->session->userdata('user_logged_info')['unlimited_senders']) { ?>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                                        <input type="text" id="sender_name" name="sender_name" value="<?php echo (empty($resent_message) ? "" : $resent_message['sender_name']); ?>" class="form-control" dir="<?php echo $this->view_settings['dir']; ?>" data-msg-required="<?php echo $this->lang->line('msg_error_required_field'); ?>" />
                                                    </div>
                                                <?php } else { ?>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                                        <select id="sender_name" name="sender_name" class="form-control" data-msg-required="<?php echo $this->lang->line('msg_error_required_field'); ?>">
                                                            <option value="<?php echo $this->site_settings['default_sender']; ?>"><?php echo $this->site_settings['default_sender']; ?></option>
                                                            <?php foreach ($senders as $sender) { ?>
                                                                <option value="<?php echo $sender['name']; ?>" <?php if ($sender['default']) echo "selected='selected'"; ?>><?php echo $sender['name']; ?></option>
                                                            <?php } ?>
                                                            <?php
                                                            if (!empty($granted_senders)) {
                                                                foreach ($granted_senders as $granted_sender) {
                                                            ?>
                                                                    <option value="<?php echo $granted_sender['name']; ?>" <?php if ($granted_sender['default']) echo "selected='selected'"; ?>><?php echo $granted_sender['name']; ?></option>
                                                            <?php
                                                                }
                                                            }
                                                            ?>
                                                        </select>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="<?php echo site_url('user/sms/form_sender'); ?>" data-target="#ajax" data-toggle="modal" class="btn btn-primary" style="width:100%; text-align:<?php echo $view_settings['float']; ?>"> <i class="fa fa-certificate"></i> <?php echo $this->lang->line('lbl_requesting_sender'); ?> </a>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><?php echo $this->lang->line('fld_numbers'); ?>
                                                <a class="" data-toggle="dropdown"><i class="fa fa-question"></i></a>
                                                <ul class="dropdown-menu bottom-up">
                                                    <li><a><?php echo $this->lang->line('msg_info_total_numbers'); ?></a></li>
                                                </ul>
                                            </label>
                                            <div class="col-md-7 gray box" id="div_all_numbers">
                                                <textarea class="form-control" name="as-values-1" id="as-values-1" cols="30" rows="10"></textarea>
                                                <!-- <input type="text" id="contact" name="name" class="form-control" /> -->
                                                <input type="hidden" id="contact_error" name="contact_error" data-msg-requiredUNLESSexcel="<?php echo $this->lang->line('msg_error_required_field'); ?>" />
                                            </div>
                                            <div class="col-md-3">
                                                <span class="btn btn-danger" style="width:100%; text-align:<?php echo $view_settings['float']; ?>" onclick="clear_numbers();"> <i class="fa fa-trash"></i> <?php echo $this->lang->line('act_clear'); ?> </span>
                                                <br> <br>
                                                <span class="btn btn-dark" style="width:100%; text-align:<?php echo $view_settings['float']; ?>" onclick="clear_numbers_input();"> <i class="fa fa-trash"></i> تهيئة </span>

                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label col-md-2"><?php echo $this->lang->line('lbl_favorit_messages'); ?></label>
                                            <div class="col-md-7">
                                                <div class="input-group">
                                                    <span class="input-group-addon"><i class="fa fa-star"></i></span>
                                                    <select id="favorites_dropown" class="form-control" onchange="onclick_favorit();" data-msg-required="<?php echo $this->lang->line('msg_error_required_field'); ?>">
                                                        <option value="" selected><?= $this->lang->line('lbl_favorit_select') ?> </option>
                                                        <?php foreach ($favorit_messages as $favorit_message) { ?>
                                                            <option value="<?= $favorit_message['text']; ?>"> <?= $favorit_message['title']; ?> </option> 
                                                        <?php } ?>

                                                    </select>
                                                </div>
                                            </div>
                                        </div>



                                        <div class="form-group">
                                            <label class="control-label col-md-2">
                                                <?php echo $this->lang->line('fld_text'); ?>
                                            </label>
                                            <div class="col-md-7">
                                                <?php if ($use_senders == 1) {
                                                ?>
                                                    <textarea id="message" name="message" class="form-control" data-msg-required="<?php echo $this->lang->line('msg_error_required_field'); ?>" rows="7"><?php echo (empty($resent_message) ? (empty($message_text) ? "" : $message_text) : $resent_message['text']); ?></textarea>
                                                <?php
                                                } else {
                                                ?>
                                                    <textarea id="message" name="message" class="form-control" data-msg-required="<?php echo $this->lang->line('msg_error_required_field'); ?>" rows="7" disabled>رسالة تجريبية من موقع dreams.sa</textarea>
                                                <?php
                                                }
                                                ?>
                                            </div>
                                            <div class="col-md-3">
                                                <br />
                                                <span class="badge badge-success" style="width:100%; margin-top:10px;"> <?php echo $this->lang->line('fld_messages_count'); ?> <font id="msg_cnt"> 0 </font></span>
                                                <span class="badge badge-success" style="width:100%; margin-top:10px;"> <?php echo $this->lang->line('lbl_consumed_chars'); ?> <font id="cons_char_cnt"> 0 </font></span>
                                                <span class="badge badge-success" style="width:100%; margin-top:10px;"> <?php echo $this->lang->line('lbl_remaining_chars'); ?> <font id="char_cnt"> 0 </font></span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2"><?php echo $this->lang->line('fld_sending_time'); ?></label>
                                            <div class="col-md-7 radio-list">
                                                <label>
                                                    <input type="radio" name="send_time_method" id="send_now" value="NOW" checked="checked" />
                                                    <?php echo $this->lang->line('lbl_now'); ?>
                                                </label>
                                                <label>
                                                    <input type="radio" name="send_time_method" id="send_later" value="LATER" />
                                                    <?php echo $this->lang->line('lbl_later'); ?>
                                                    <br /><br />
                                                    <div id="sendLaterContainer" class="hidden">
                                                        <div class="input-group date form_datetime col-md-12">
                                                            <input type="text" size="16" readonly class="form-control" name="send_time" id="send_time" data-msg-requiredIFlater="<?php echo $this->lang->line('msg_error_required_field'); ?>" style="direction:ltr; text-align:center;" />
                                                            <span class="input-group-btn">
                                                                <button class="btn default date-set" type="button" style="margin-right:-48px; height:30px"><i class="fa fa-calendar"></i></button>
                                                            </span>
                                                        </div>
                                                        <br />

                                                        <!--Repeat message-->
                                                        <div class="col-md-12 hidden">
                                                            <div class="col-md-3">
                                                                <?php echo $this->lang->line('lbl_message_repeation'); ?>
                                                            </div>
                                                            <div class="col-md-2">
                                                                <select id="repeation_period" name="repeation_period" class="form-control">
                                                                    <option value="0"><?php echo $this->lang->line('lbl_none'); ?></option>
                                                                    <option value="DAILY"><?php echo $this->lang->line('lbl_daily'); ?></option>
                                                                    <option value="WEEKLY"><?php echo $this->lang->line('lbl_weekly'); ?></option>
                                                                    <option value="MONTHLY"><?php echo $this->lang->line('lbl_monthly'); ?></option>
                                                                    <option value="YEARLY"><?php echo $this->lang->line('lbl_yearly'); ?></option>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-1">
                                                                <?php echo $this->lang->line('lbl_for'); ?>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="repeation_times" id="repeation_times" value='1' data-msg-number="<?php echo $this->lang->line('msg_error_number'); ?>" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-1">
                                                                <?php echo $this->lang->line('lbl_additional_once'); ?>
                                                            </div>
                                                        </div>
                                                        <!--<br/><br/><br/>-->
                                                        <span id="server-time" class="btn green col-md-12"><?php echo $this->lang->line('lbl_loading'); ?></span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-actions">
                                            <div class="row">
                                                <div class="col-md-2"></div>
                                                <div class="col-md-7">
                                                    <input type="hidden" name="sms_type" id="sms_type" value="<?php
                                                                                                                if (!empty($type)) {
                                                                                                                    echo $type;
                                                                                                                }
                                                                                                                ?>" />
                                                    <button type="submit" class="btn blue col-md-12"> <i class="fa fa-send"> </i><?php echo $this->lang->line('act_send'); ?></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="hidden">
    <form id="exc_upload" name="exc_upload" action="<?php echo site_url("/user/sms/upload_excel"); ?>" method="POST" enctype="multipart/form-data">
        <input type="file" size="20" name="excel_file" id="excel_file" class="hidden" />
        <input type="hidden" name="sms_type" id="sms_type" value="<?php
                                                                    if (!empty($type)) {
                                                                        echo $type;
                                                                    }
                                                                    ?>" />
    </form>
</div>

<div class="modal fade" id="modal_statistics" role="basic" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo $this->lang->line('lbl_sending_details'); ?></h4>
            </div>
            <div class="modal-body" id="modal_body_statistics"></div>
            <div class="modal-footer"><button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('act_cancel'); ?></button></div>
        </div>
    </div>
</div>

<div class="modal fade" id="ajax" role="basic" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                ddddddddddddddd
                <img src="<?php echo base_url('/assets/metronic'); ?>/global/img/loading-spinner-grey.gif" alt="" class="loading" />
                <span> &nbsp;&nbsp;Loading... </span>
            </div>
        </div>
    </div>
</div>

<!--Script by Shahaj-->
<script type="text/javascript">
    function getTotalCount(parent_id) {
        if (parent_id) {
            $.ajax({
                type: "GET",
                url: "<?php echo base_url('user/sms/ads_getCounts'); ?>",
                data: {
                    parent_id: parent_id
                },
                dataType: "json",
                success: function(result) {
                    if (result) {
                        $.each(result, function(index, value) {
                            $("#totalCount_" + value.id).html("- " + value.total_sub);
                        });
                    }
                }
            });
        }


    }
</script>