<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SmsProcessing
{
    protected $CI;
    protected $advertising = false;

    protected function initialize()
    {
        $this->CI =& get_instance();

        // Load database
        $this->CI->load->database();

        // Load all required models
        $required_models = ['user', 'outbox', 'message'];
        foreach ($required_models as $model) {
            $this->CI->load->model($model);
        }

        // Load other dependencies
        $this->CI->load->service('sms_processing_service');
        $this->CI->load->service('AdminNotificationService');
        $this->CI->load->validation('AdvertisingValidator');
        $this->CI->load->helper('message');
        $this->CI->load->language('lang');
    }
    public function handle($data)
    {

        $this->initialize();
        $jobId = $data->job_id;
        $job = $this->CI->db->get_where('sms_processing_jobs', ['id' => $jobId])->row();
        if (!$job) {
            return;
        }
        try {
            $this->CI->db->update('sms_processing_jobs', [
                'status' => 'processing',
                'started_at' => date('Y-m-d H:i:s')
            ], ['id' => $jobId]);
            $request = json_decode($job->request_data, true);
            $this->CI->message->user();
            $user_info = $this->CI->user->get_by_id($job->user_id);
            if (isset($request['sms_type']) && $request['sms_type'] === 'CALENDAR') {
                $coordinates = get_lat_long($request['google_map_url']);
                $calendar = get_calendar_url(
                    $user_info->id,
                    $request['sender_name'],
                    $request['message'],
                    $request['calendar_time'],
                    $request['reminder'],
                    $request['reminder_text'],
                    $coordinates['lat'],
                    $coordinates['long'],
                    extractLocationTitle($request['google_map_url'])
                );
                if (isset($calendar['status']) && $calendar['status'] == 1 && isset($calendar['url'])) {
                    $request['message'] .= "\n" . $calendar['url'];
                } else {
                    throw new Exception("Failed to generate calendar URL");
                }
            }
            $request['job_id'] = $jobId;
            $responseData = $this->CI->sms_processing_service->processRequest($request, $user_info);
            if (!isset($responseData['totalCost'])) {
                throw new Exception("error in cost");
            }
            if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $responseData['totalCost']) {
                throw new Exception($this->CI->lang->line('msg_error_insufficient_balance'));
            }

            //Start transaction
            $this->CI->db->db_debug = false;
            $this->CI->db->trans_begin();
            $deduct_balance = $user_info->change_balance((-1 * round($responseData['totalCost'], 1)), "Send SMS Campaign");

            if (!$deduct_balance) {
                throw new Exception($this->CI->lang->line('msg_error_insufficient_balance'));
            }

           $outbox_id = $this->saveOutbox($responseData, $user_info);

            $this->CI->db->update('sms_processing_jobs', [
                'status' => 'completed',
                'completed_at' => date('Y-m-d H:i:s')
            ], ['id' => $jobId]);
            $this->CI->db->trans_commit();
            if($this->advertising){
                 $this->CI->AdminNotificationService->notifyAdminForError();
            }

        } catch (Exception $e) {
            $this->CI->db->trans_rollback();
            $this->CI->db->update('sms_processing_jobs', [
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => date('Y-m-d H:i:s')
            ], ['id' => $jobId]);

            // Notify admin about job failure
            $this->CI->AdminNotificationService->notifyAdminForError();
        }
    }

    private function saveOutbox($data, $user_info)
    {
        $this->CI->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $this->advertising = $this->CI->AdvertisingValidator->validate($data, $user_info);
        $outbox_param = [
            'id' => null,
            'channel' => 'DIRECT',
            'user_id' => $user_info->id,
            'text' => $data['message'],
            'count' => $data['totalNumbers'],
            'cost' => $data['totalCost'],
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $data['send_time_method'] == "LATER" ? $data['send_time'] : NULL,
            'repeation_period' => $data['send_time_method'] == "LATER" ? $data['repeation_period'] : "0",
            'repeation_times' => $data['send_time_method'] == "LATER" ? $data['repeation_times'] : 0,
            'variables_message' => ($data['sms_type'] == "VARIABLES" ? 1 : 0),
            'sender_name' => $data['sender_name'],
            'excel_file_numbers' => $data['excel_file_numbers'],
            'all_numbers' => $data['all_numbers'],
            'number_index' => null,
            'encrypted' => $data['sms_type'] == "ADS" ? 2 : 0,
            'auth_code' => $auth_code,
            'advertising' => $this->advertising
        ];

        $outbox_id = $this->CI->outbox->insert_by_array($outbox_param);
        if (empty($outbox_id)) {
            throw new Exception("Insert failed to outbox");
        }
        $message_id = $this->pre_message_flush_by_auth_code($outbox_param);
        $outbox_param = [
            'id' => $outbox_id,
            'message_id' => $message_id,
            'updated_time' => date('Y-m-d H:i:s')
        ];

        $update_outbox = $this->CI->outbox->update_status($outbox_param);
        if (empty($update_outbox)) {
            throw new Exception("Update failed to outbox");
        }

        return $outbox_id;
    }

    private function pre_message_flush_by_auth_code($outbox_param)
    {
        //Insert to message tb
        $message_param = [
            'user_id' => $outbox_param['user_id'],
            'channel' => $outbox_param['channel'],
            'text' => $outbox_param['text'],
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $outbox_param['sending_datetime'],
            'variables_message' => $outbox_param['variables_message'],
            'count' => $outbox_param['count'],
            'cost' => $outbox_param['cost'],
            'sender_name' => $outbox_param['sender_name'],
            'auth_code' => $outbox_param['auth_code'],
            'advertising' => $outbox_param['advertising'],
            'status' => $outbox_param['variables_message'] ==1 ? 1:0,
        ];

        $message_id = $this->CI->message->insert_by_array($message_param);
        if (empty($message_id)) {
            throw new Exception("Message insert failed");
        }
        return $message_id;
    }

}