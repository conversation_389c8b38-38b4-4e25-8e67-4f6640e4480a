<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-01 01:02:41 --> Severity: error --> Exception: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 C:\laragon\www\smsportal\application\libraries\RedisQueue.php 19
ERROR - 2025-06-01 01:02:54 --> Session: Unable to obtain lock for ci_session:aac37r55kclnvjr70mrf11696gdatpld after 30 attempts, aborting.
ERROR - 2025-06-01 01:02:54 --> Severity: Warning --> session_start(): Failed to read session data: user (path: C:/laragon/tmp) C:\laragon\www\smsportal\system\libraries\Session\Session.php 140
ERROR - 2025-06-01 01:02:55 --> Session: Unable to obtain lock for ci_session:aac37r55kclnvjr70mrf11696gdatpld after 30 attempts, aborting.
ERROR - 2025-06-01 01:02:55 --> Severity: Warning --> session_start(): Failed to read session data: user (path: C:/laragon/tmp) C:\laragon\www\smsportal\system\libraries\Session\Session.php 140
ERROR - 2025-06-01 01:02:57 --> Session: Unable to obtain lock for ci_session:aac37r55kclnvjr70mrf11696gdatpld after 30 attempts, aborting.
ERROR - 2025-06-01 01:02:57 --> Severity: Warning --> session_start(): Failed to read session data: user (path: C:/laragon/tmp) C:\laragon\www\smsportal\system\libraries\Session\Session.php 140
ERROR - 2025-06-01 01:03:02 --> Severity: error --> Exception: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 C:\laragon\www\smsportal\application\libraries\RedisQueue.php 19
ERROR - 2025-06-01 01:16:54 --> Severity: error --> Exception: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 C:\laragon\www\smsportal\application\libraries\RedisQueue.php 19
