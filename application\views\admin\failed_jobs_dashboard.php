<link href="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap<?php echo $view_settings['css_suffix'];?>.css" rel="stylesheet" type="text/css" />
<script src="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>

<div class="container-fluid">
    <!-- BEGIN BREADCRUMBS -->
    <div class="breadcrumbs">
        <h1>Failed Jobs Management</h1>
        <ol class="breadcrumb">
            <li><a>Home</a></li>
            <li><a>System</a></li>
            <li class="active">Failed Jobs</li>
        </ol>
    </div>
    <!-- END BREADCRUMBS -->

    <!-- BEGIN PAGE CONTENT -->
    <div class="page-content-container">
        <div class="page-content-row">
            <div class="page-content-col">
                
                <!-- Queue Statistics -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-bar-chart font-green"></i>
                                    <span class="caption-subject font-green bold uppercase">Queue Statistics</span>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <div class="row">
                                    <?php foreach ($queue_stats as $queue => $stats): ?>
                                    <div class="col-md-3">
                                        <div class="dashboard-stat2 bordered">
                                            <div class="display">
                                                <div class="number">
                                                    <h3 class="font-<?php echo $stats['failed'] > 0 ? 'red' : 'green'; ?>">
                                                        <?php echo $stats['failed']; ?>
                                                    </h3>
                                                    <small>Failed Jobs</small>
                                                </div>
                                                <div class="icon">
                                                    <i class="fa fa-exclamation-triangle"></i>
                                                </div>
                                            </div>
                                            <div class="progress-info">
                                                <div class="progress">
                                                    <span style="width: 100%;" class="progress-bar progress-bar-<?php echo $stats['failed'] > 0 ? 'danger' : 'success'; ?>">
                                                        <span class="sr-only">100% Complete</span>
                                                    </span>
                                                </div>
                                                <div class="status">
                                                    <div class="status-title">Queue: <?php echo ucfirst($queue); ?></div>
                                                    <div class="status-number">
                                                        Pending: <?php echo $stats['pending']; ?> | 
                                                        Processing: <?php echo $stats['processing']; ?> | 
                                                        Delayed: <?php echo $stats['delayed']; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    
                                    <!-- Database Failed Messages -->
                                    <div class="col-md-3">
                                        <div class="dashboard-stat2 bordered">
                                            <div class="display">
                                                <div class="number">
                                                    <h3 class="font-<?php echo $db_failed_count > 0 ? 'red' : 'green'; ?>">
                                                        <?php echo $db_failed_count; ?>
                                                    </h3>
                                                    <small>DB Failed Messages</small>
                                                </div>
                                                <div class="icon">
                                                    <i class="fa fa-database"></i>
                                                </div>
                                            </div>
                                            <div class="progress-info">
                                                <div class="progress">
                                                    <span style="width: 100%;" class="progress-bar progress-bar-<?php echo $db_failed_count > 0 ? 'danger' : 'success'; ?>">
                                                        <span class="sr-only">100% Complete</span>
                                                    </span>
                                                </div>
                                                <div class="status">
                                                    <div class="status-title">Database</div>
                                                    <div class="status-number">Legacy failed messages</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Jobs Management Tabs -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-list font-blue"></i>
                                    <span class="caption-subject font-blue bold uppercase">Jobs Management</span>
                                </div>
                                <div class="actions">
                                    <div class="btn-group">
                                        <select id="queue-selector" class="form-control input-sm">
                                            <?php foreach (array_keys($queue_stats) as $queue): ?>
                                            <option value="<?php echo $queue; ?>"><?php echo ucfirst($queue); ?> Queue</option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button class="btn btn-sm btn-warning" id="retry-all-btn">
                                        <i class="fa fa-refresh"></i> Retry All
                                    </button>
                                    <button class="btn btn-sm btn-danger" id="clear-queue-btn">
                                        <i class="fa fa-trash"></i> Clear Queue
                                    </button>
                                    <a href="<?php echo site_url('admin/FailedJobs/export_csv'); ?>" class="btn btn-sm btn-success" id="export-csv-btn">
                                        <i class="fa fa-download"></i> Export CSV
                                    </a>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <!-- Tabs Navigation -->
                                <ul class="nav nav-tabs" role="tablist">
                                    <li class="active">
                                        <a href="#failed-jobs-tab" role="tab" data-toggle="tab">
                                            <i class="fa fa-exclamation-triangle"></i> Failed Jobs
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#processing-jobs-tab" role="tab" data-toggle="tab">
                                            <i class="fa fa-cog fa-spin"></i> Processing Jobs
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#pending-jobs-tab" role="tab" data-toggle="tab">
                                            <i class="fa fa-clock-o"></i> Pending Jobs
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#database-jobs-tab" role="tab" data-toggle="tab">
                                            <i class="fa fa-database"></i> Database Failed Jobs
                                        </a>
                                    </li>
                                </ul>

                                <!-- Tab Content -->
                                <div class="tab-content">
                                    <!-- Failed Jobs Tab -->
                                    <div class="tab-pane active" id="failed-jobs-tab">
                                        <table class="table table-striped table-bordered table-hover" id="failed-jobs-table">
                                            <thead>
                                                <tr>
                                                    <th>Job ID</th>
                                                    <th>Job Class</th>
                                                    <th>Attempts</th>
                                                    <th>Created At</th>
                                                    <th>Job Data</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Processing Jobs Tab -->
                                    <div class="tab-pane" id="processing-jobs-tab">
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i>
                                            These are jobs currently being processed by workers. Jobs processing for more than 30 minutes are marked as "Stuck".
                                        </div>
                                        <table class="table table-striped table-bordered table-hover" id="processing-jobs-table">
                                            <thead>
                                                <tr>
                                                    <th>Job ID</th>
                                                    <th>Job Class</th>
                                                    <th>Attempts</th>
                                                    <th>Started At</th>
                                                    <th>Processing Time</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pending Jobs Tab -->
                                    <div class="tab-pane" id="pending-jobs-tab">
                                        <div class="alert alert-warning">
                                            <i class="fa fa-warning"></i>
                                            These are jobs waiting to be processed. Use caution when clearing pending jobs.
                                        </div>
                                        <table class="table table-striped table-bordered table-hover" id="pending-jobs-table">
                                            <thead>
                                                <tr>
                                                    <th>Queue</th>
                                                    <th>Count</th>
                                                    <th>Status</th>
                                                    <th>Information</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Database Failed Jobs Tab -->
                                    <div class="tab-pane" id="database-jobs-tab">
                                        <div class="alert alert-success">
                                            <i class="fa fa-database"></i>
                                            These are failed jobs stored in the database for persistent tracking and analysis.
                                        </div>
                                        <table class="table table-striped table-bordered table-hover" id="database-jobs-table">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>UUID</th>
                                                    <th>Job Class</th>
                                                    <th>Queue</th>
                                                    <th>Attempts</th>
                                                    <th>Failed At</th>
                                                    <th>Error</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="job-details-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Job Details</h4>
            </div>
            <div class="modal-body">
                <div id="job-details-content">
                    <!-- Job details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTables
    var failedJobsTable = $('#failed-jobs-table').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('admin/FailedJobs/load_data'); ?>",
            "type": "POST",
            "data": function(d) {
                d.queue = $('#queue-selector').val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "job"},
            {"data": "attempts"},
            {"data": "created_at"},
            {
                "data": "data",
                "render": function(data, type, row) {
                    if (data.length > 50) {
                        return data.substring(0, 50) + '...';
                    }
                    return data;
                }
            },
            {"data": "actions", "orderable": false}
        ],
        "order": [[3, "desc"]],
        "pageLength": 25
    });

    var processingJobsTable = $('#processing-jobs-table').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('admin/FailedJobs/load_processing_data'); ?>",
            "type": "POST",
            "data": function(d) {
                d.queue = $('#queue-selector').val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "job"},
            {"data": "attempts"},
            {"data": "created_at"},
            {"data": "processing_time"},
            {
                "data": "status",
                "render": function(data, type, row) {
                    if (data === 'Stuck') {
                        return '<span class="label label-danger">' + data + '</span>';
                    }
                    return '<span class="label label-info">' + data + '</span>';
                }
            },
            {"data": "actions", "orderable": false}
        ],
        "order": [[3, "desc"]],
        "pageLength": 25
    });

    var pendingJobsTable = $('#pending-jobs-table').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('admin/FailedJobs/load_pending_data'); ?>",
            "type": "POST",
            "data": function(d) {
                d.queue = $('#queue-selector').val();
            }
        },
        "columns": [
            {"data": "queue"},
            {"data": "count"},
            {"data": "status"},
            {"data": "info"},
            {"data": "actions", "orderable": false}
        ],
        "pageLength": 25
    });

    var databaseJobsTable = $('#database-jobs-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo site_url('admin/FailedJobs/load_database_data'); ?>",
            "type": "POST",
            "data": function(d) {
                d.queue = $('#queue-selector').val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "uuid"},
            {"data": "job_class"},
            {"data": "queue"},
            {"data": "attempts"},
            {"data": "failed_at"},
            {
                "data": "error_message",
                "render": function(data, type, row) {
                    if (data.length > 50) {
                        return data.substring(0, 50) + '...';
                    }
                    return data;
                }
            },
            {"data": "actions", "orderable": false}
        ],
        "order": [[5, "desc"]],
        "pageLength": 25
    });

    // Reload tables when queue changes
    $('#queue-selector').change(function() {
        failedJobsTable.ajax.reload();
        processingJobsTable.ajax.reload();
        pendingJobsTable.ajax.reload();
        databaseJobsTable.ajax.reload();
        updateExportLink();
    });

    // Tab change handler - reload table when tab is shown
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        if (target === '#processing-jobs-tab') {
            processingJobsTable.ajax.reload();
        } else if (target === '#pending-jobs-tab') {
            pendingJobsTable.ajax.reload();
        } else if (target === '#database-jobs-tab') {
            databaseJobsTable.ajax.reload();
        } else if (target === '#failed-jobs-tab') {
            failedJobsTable.ajax.reload();
        }
    });

    // Update export CSV link
    function updateExportLink() {
        var queue = $('#queue-selector').val();
        $('#export-csv-btn').attr('href', '<?php echo site_url('admin/FailedJobs/export_csv'); ?>?queue=' + queue);
    }

    // View job details
    $(document).on('click', '.view-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        $.post('<?php echo site_url('admin/FailedJobs/get_job_details'); ?>', {
            job_id: jobId,
            queue: queue
        }, function(response) {
            if (response.success) {
                var job = response.job;
                var html = '<div class="row">';
                html += '<div class="col-md-6"><strong>Job ID:</strong> ' + job.id + '</div>';
                html += '<div class="col-md-6"><strong>Job Class:</strong> ' + job.job + '</div>';
                html += '<div class="col-md-6"><strong>Attempts:</strong> ' + job.attempts + '</div>';
                html += '<div class="col-md-6"><strong>Created At:</strong> ' + job.created_at + '</div>';
                html += '</div><hr>';
                html += '<h5>Job Data:</h5>';
                html += '<pre>' + JSON.stringify(job.data, null, 2) + '</pre>';
                
                $('#job-details-content').html(html);
                $('#job-details-modal').modal('show');
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    });

    // Retry single job
    $(document).on('click', '.retry-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        bootbox.confirm({
            message: 'Are you sure you want to retry this job?',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-warning'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/retry_job'); ?>', {
                        job_id: jobId,
                        queue: queue
                    }, function(response) {
                        if (response.success) {
                            bootbox.alert('Job retried successfully');
                            table.ajax.reload();
                        } else {
                            bootbox.alert('Error: ' + response.message);
                        }
                    }, 'json');
                }
            }
        });
        
    });

    // Delete single job
    $(document).on('click', '.delete-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        bootbox.confirm({
            message: 'Are you sure you want to delete this job? This action cannot be undone.',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/delete_job'); ?>', {
                        job_id: jobId,
                        queue: queue
                    }, function(response) {
                        if (response.success) {
                            alert('Job deleted successfully');
                            table.ajax.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    }, 'json');
                }
            }
        });
    });

    // Retry all jobs
    $('#retry-all-btn').click(function() {
        var queue = $('#queue-selector').val();
        
        bootbox.confirm({
            message: 'Are you sure you want to retry all failed jobs in the ' + queue + ' queue?',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-warning'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/retry_all'); ?>', {
                        queue: queue
                    }, function(response) {
                        alert(response.message);
                        if (response.success) {
                            table.ajax.reload();
                            location.reload(); // Reload to update statistics
                        }
                    }, 'json');
                }
            }
        });
    });

    // Clear queue
    $('#clear-queue-btn').click(function() {
        var queue = $('#queue-selector').val();
        
        bootbox.confirm({
            message: 'Are you sure you want to clear all failed jobs in the ' + queue + ' queue? This action cannot be undone.',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/clear_queue'); ?>', {
                        queue: queue
                    }, function(response) {
                        alert(response.message);
                        if (response.success) {
                            table.ajax.reload();
                            location.reload(); // Reload to update statistics
                        }
                    }, 'json');
                }
            }
        });
    });

    // Processing job event handlers
    $(document).on('click', '.view-processing-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');

        $.post('<?php echo site_url('admin/FailedJobs/get_processing_job_details'); ?>', {
            job_id: jobId,
            queue: queue
        }, function(response) {
            if (response.success) {
                var job = response.job;
                var html = '<div class="row">';
                html += '<div class="col-md-6"><strong>Job ID:</strong> ' + job.id + '</div>';
                html += '<div class="col-md-6"><strong>Job Class:</strong> ' + job.job + '</div>';
                html += '<div class="col-md-6"><strong>Attempts:</strong> ' + job.attempts + '</div>';
                html += '<div class="col-md-6"><strong>Started At:</strong> ' + job.created_at + '</div>';
                html += '</div><hr>';
                html += '<h5>Job Data:</h5>';
                html += '<pre>' + JSON.stringify(job.data, null, 2) + '</pre>';

                $('#job-details-content').html(html);
                $('#job-details-modal').modal('show');
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    });

    $(document).on('click', '.force-fail-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');

        bootbox.confirm({
            message: 'Are you sure you want to force fail this processing job?',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/force_fail_processing_job'); ?>', {
                        job_id: jobId,
                        queue: queue
                    }, function(response) {
                        if (response.success) {
                            bootbox.alert('Job moved to failed queue successfully');
                            processingJobsTable.ajax.reload();
                            failedJobsTable.ajax.reload();
                        } else {
                            bootbox.alert('Error: ' + response.message);
                        }
                    }, 'json');
                }
            }
        });
    });

    // Pending jobs event handlers
    $(document).on('click', '.clear-pending-queue', function() {
        var queue = $(this).data('queue');

        bootbox.confirm({
            message: 'Are you sure you want to clear all pending jobs in the ' + queue + ' queue? This action cannot be undone.',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/clear_pending_queue'); ?>', {
                        queue: queue
                    }, function(response) {
                        bootbox.alert(response.message);
                        if (response.success) {
                            pendingJobsTable.ajax.reload();
                            location.reload(); // Reload to update statistics
                        }
                    }, 'json');
                }
            }
        });
    });

    // Database jobs event handlers
    $(document).on('click', '.view-db-job', function() {
        var jobId = $(this).data('job-id');

        $.post('<?php echo site_url('admin/FailedJobs/get_database_job_details'); ?>', {
            job_id: jobId
        }, function(response) {
            if (response.success) {
                var job = response.job;
                var html = '<div class="row">';
                html += '<div class="col-md-6"><strong>ID:</strong> ' + job.id + '</div>';
                html += '<div class="col-md-6"><strong>UUID:</strong> ' + job.uuid + '</div>';
                html += '<div class="col-md-6"><strong>Job Class:</strong> ' + job.job_class + '</div>';
                html += '<div class="col-md-6"><strong>Queue:</strong> ' + job.queue + '</div>';
                html += '<div class="col-md-6"><strong>Attempts:</strong> ' + job.attempts + '</div>';
                html += '<div class="col-md-6"><strong>Failed At:</strong> ' + job.failed_at + '</div>';
                html += '</div><hr>';
                html += '<h5>Error Message:</h5>';
                html += '<div class="alert alert-danger">' + (job.error_message || 'No error message') + '</div>';
                if (job.error_file) {
                    html += '<p><strong>Error Location:</strong> ' + job.error_file + ':' + job.error_line + '</p>';
                }
                html += '<h5>Job Data:</h5>';
                html += '<pre>' + (job.job_data || 'No job data') + '</pre>';
                if (job.stack_trace) {
                    html += '<h5>Stack Trace:</h5>';
                    html += '<pre style="max-height: 200px; overflow-y: auto;">' + job.stack_trace + '</pre>';
                }

                $('#job-details-content').html(html);
                $('#job-details-modal').modal('show');
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    });

    $(document).on('click', '.delete-db-job', function() {
        var jobId = $(this).data('job-id');

        bootbox.confirm({
            message: 'Are you sure you want to delete this database job record? This action cannot be undone.',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-default'
                }
            },
            callback: function(result) {
                if (result) {
                    $.post('<?php echo site_url('admin/FailedJobs/delete_database_job'); ?>', {
                        job_id: jobId
                    }, function(response) {
                        if (response.success) {
                            bootbox.alert('Database job deleted successfully');
                            databaseJobsTable.ajax.reload();
                        } else {
                            bootbox.alert('Error: ' + response.message);
                        }
                    }, 'json');
                }
            }
        });
    });

    // Initialize export link
    updateExportLink();
});
</script>
