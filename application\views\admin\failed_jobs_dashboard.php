<link href="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap<?php echo $view_settings['css_suffix'];?>.css" rel="stylesheet" type="text/css" />
<script src="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
<script src="<?php echo base_url('/assets/metronic');?>/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>

<div class="container-fluid">
    <!-- BEGIN BREADCRUMBS -->
    <div class="breadcrumbs">
        <h1>Failed Jobs Management</h1>
        <ol class="breadcrumb">
            <li><a>Home</a></li>
            <li><a>System</a></li>
            <li class="active">Failed Jobs</li>
        </ol>
    </div>
    <!-- END BREADCRUMBS -->

    <!-- BEGIN PAGE CONTENT -->
    <div class="page-content-container">
        <div class="page-content-row">
            <div class="page-content-col">
                
                <!-- Queue Statistics -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-bar-chart font-green"></i>
                                    <span class="caption-subject font-green bold uppercase">Queue Statistics</span>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <div class="row">
                                    <?php foreach ($queue_stats as $queue => $stats): ?>
                                    <div class="col-md-3">
                                        <div class="dashboard-stat2 bordered">
                                            <div class="display">
                                                <div class="number">
                                                    <h3 class="font-<?php echo $stats['failed'] > 0 ? 'red' : 'green'; ?>">
                                                        <?php echo $stats['failed']; ?>
                                                    </h3>
                                                    <small>Failed Jobs</small>
                                                </div>
                                                <div class="icon">
                                                    <i class="fa fa-exclamation-triangle"></i>
                                                </div>
                                            </div>
                                            <div class="progress-info">
                                                <div class="progress">
                                                    <span style="width: 100%;" class="progress-bar progress-bar-<?php echo $stats['failed'] > 0 ? 'danger' : 'success'; ?>">
                                                        <span class="sr-only">100% Complete</span>
                                                    </span>
                                                </div>
                                                <div class="status">
                                                    <div class="status-title">Queue: <?php echo ucfirst($queue); ?></div>
                                                    <div class="status-number">
                                                        Pending: <?php echo $stats['pending']; ?> | 
                                                        Processing: <?php echo $stats['processing']; ?> | 
                                                        Delayed: <?php echo $stats['delayed']; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    
                                    <!-- Database Failed Messages -->
                                    <div class="col-md-3">
                                        <div class="dashboard-stat2 bordered">
                                            <div class="display">
                                                <div class="number">
                                                    <h3 class="font-<?php echo $db_failed_count > 0 ? 'red' : 'green'; ?>">
                                                        <?php echo $db_failed_count; ?>
                                                    </h3>
                                                    <small>DB Failed Messages</small>
                                                </div>
                                                <div class="icon">
                                                    <i class="fa fa-database"></i>
                                                </div>
                                            </div>
                                            <div class="progress-info">
                                                <div class="progress">
                                                    <span style="width: 100%;" class="progress-bar progress-bar-<?php echo $db_failed_count > 0 ? 'danger' : 'success'; ?>">
                                                        <span class="sr-only">100% Complete</span>
                                                    </span>
                                                </div>
                                                <div class="status">
                                                    <div class="status-title">Database</div>
                                                    <div class="status-number">Legacy failed messages</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Failed Jobs Table -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="portlet light bordered">
                            <div class="portlet-title">
                                <div class="caption">
                                    <i class="fa fa-list font-red"></i>
                                    <span class="caption-subject font-red bold uppercase">Failed Jobs</span>
                                </div>
                                <div class="actions">
                                    <div class="btn-group">
                                        <select id="queue-selector" class="form-control input-sm">
                                            <?php foreach (array_keys($queue_stats) as $queue): ?>
                                            <option value="<?php echo $queue; ?>"><?php echo ucfirst($queue); ?> Queue</option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button class="btn btn-sm btn-warning" id="retry-all-btn">
                                        <i class="fa fa-refresh"></i> Retry All
                                    </button>
                                    <button class="btn btn-sm btn-danger" id="clear-queue-btn">
                                        <i class="fa fa-trash"></i> Clear Queue
                                    </button>
                                    <a href="<?php echo site_url('admin/FailedJobs/export_csv'); ?>" class="btn btn-sm btn-success" id="export-csv-btn">
                                        <i class="fa fa-download"></i> Export CSV
                                    </a>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <table class="table table-striped table-bordered table-hover" id="failed-jobs-table">
                                    <thead>
                                        <tr>
                                            <th>Job ID</th>
                                            <th>Job Class</th>
                                            <th>Attempts</th>
                                            <th>Created At</th>
                                            <th>Job Data</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="job-details-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Job Details</h4>
            </div>
            <div class="modal-body">
                <div id="job-details-content">
                    <!-- Job details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#failed-jobs-table').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('admin/FailedJobs/load_data'); ?>",
            "type": "POST",
            "data": function(d) {
                d.queue = $('#queue-selector').val();
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "job"},
            {"data": "attempts"},
            {"data": "created_at"},
            {
                "data": "data",
                "render": function(data, type, row) {
                    if (data.length > 50) {
                        return data.substring(0, 50) + '...';
                    }
                    return data;
                }
            },
            {"data": "actions", "orderable": false}
        ],
        "order": [[3, "desc"]],
        "pageLength": 25
    });

    // Reload table when queue changes
    $('#queue-selector').change(function() {
        table.ajax.reload();
        updateExportLink();
    });

    // Update export CSV link
    function updateExportLink() {
        var queue = $('#queue-selector').val();
        $('#export-csv-btn').attr('href', '<?php echo site_url('admin/FailedJobs/export_csv'); ?>?queue=' + queue);
    }

    // View job details
    $(document).on('click', '.view-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        $.post('<?php echo site_url('admin/FailedJobs/get_job_details'); ?>', {
            job_id: jobId,
            queue: queue
        }, function(response) {
            if (response.success) {
                var job = response.job;
                var html = '<div class="row">';
                html += '<div class="col-md-6"><strong>Job ID:</strong> ' + job.id + '</div>';
                html += '<div class="col-md-6"><strong>Job Class:</strong> ' + job.job + '</div>';
                html += '<div class="col-md-6"><strong>Attempts:</strong> ' + job.attempts + '</div>';
                html += '<div class="col-md-6"><strong>Created At:</strong> ' + job.created_at + '</div>';
                html += '</div><hr>';
                html += '<h5>Job Data:</h5>';
                html += '<pre>' + JSON.stringify(job.data, null, 2) + '</pre>';
                
                $('#job-details-content').html(html);
                $('#job-details-modal').modal('show');
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    });

    // Retry single job
    $(document).on('click', '.retry-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        if (confirm('Are you sure you want to retry this job?')) {
            $.post('<?php echo site_url('admin/FailedJobs/retry_job'); ?>', {
                job_id: jobId,
                queue: queue
            }, function(response) {
                if (response.success) {
                    alert('Job retried successfully');
                    table.ajax.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            }, 'json');
        }
    });

    // Delete single job
    $(document).on('click', '.delete-job', function() {
        var jobId = $(this).data('job-id');
        var queue = $(this).data('queue');
        
        if (confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
            $.post('<?php echo site_url('admin/FailedJobs/delete_job'); ?>', {
                job_id: jobId,
                queue: queue
            }, function(response) {
                if (response.success) {
                    alert('Job deleted successfully');
                    table.ajax.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            }, 'json');
        }
    });

    // Retry all jobs
    $('#retry-all-btn').click(function() {
        var queue = $('#queue-selector').val();
        
        if (confirm('Are you sure you want to retry all failed jobs in the ' + queue + ' queue?')) {
            $.post('<?php echo site_url('admin/FailedJobs/retry_all'); ?>', {
                queue: queue
            }, function(response) {
                alert(response.message);
                if (response.success) {
                    table.ajax.reload();
                    location.reload(); // Reload to update statistics
                }
            }, 'json');
        }
    });

    // Clear queue
    $('#clear-queue-btn').click(function() {
        var queue = $('#queue-selector').val();
        
        if (confirm('Are you sure you want to clear all failed jobs in the ' + queue + ' queue? This action cannot be undone.')) {
            $.post('<?php echo site_url('admin/FailedJobs/clear_queue'); ?>', {
                queue: queue
            }, function(response) {
                alert(response.message);
                if (response.success) {
                    table.ajax.reload();
                    location.reload(); // Reload to update statistics
                }
            }, 'json');
        }
    });

    // Initialize export link
    updateExportLink();
});
</script>
