-- Create failed_jobs table for persistent storage of failed jobs
-- Run this SQL script in your database to create the table

CREATE TABLE IF NOT EXISTS `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` varchar(255) NOT NULL DEFAULT 'redis',
  `queue` varchar(255) NOT NULL DEFAULT 'default',
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `job_class` varchar(255) NOT NULL,
  `job_data` longtext,
  `attempts` int(11) NOT NULL DEFAULT 1,
  `error_message` text,
  `error_file` varchar(500),
  `error_line` int(11),
  `stack_trace` longtext,
  `worker_id` varchar(100),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMAR<PERSON> (`id`),
  <PERSON>IQUE KEY `failed_jobs_uuid_unique` (`uuid`),
  <PERSON><PERSON>Y `failed_jobs_queue_index` (`queue`),
  <PERSON><PERSON><PERSON> `failed_jobs_job_class_index` (`job_class`),
  KEY `failed_jobs_failed_at_index` (`failed_at`),
  KEY `idx_failed_jobs_queue_failed_at` (`queue`, `failed_at`),
  KEY `idx_failed_jobs_job_class_failed_at` (`job_class`, `failed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert a test record to verify the table works
-- You can remove this after testing
INSERT INTO `failed_jobs` (
    `uuid`, `connection`, `queue`, `payload`, `exception`, 
    `job_class`, `job_data`, `attempts`, `error_message`, 
    `failed_at`, `created_at`, `updated_at`
) VALUES (
    'test-uuid-12345', 'redis', 'default', 
    '{"id":"test","job":"TestJob","data":{"test":true}}',
    'Test exception for table verification',
    'TestJob', '{"test":true}', 1, 
    'This is a test record to verify the table was created successfully',
    NOW(), NOW(), NOW()
);

-- Verify the table was created successfully
SELECT 'Failed jobs table created successfully!' as status, COUNT(*) as test_records FROM failed_jobs;
