<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DatabaseFailedJobs extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Only allow CLI access
        if (!$this->input->is_cli_request()) {
            show_error('This script can only be accessed via command line.');
        }
        
        $this->load->model('Failed_job_model');
        $this->load->library('RedisQueue');
    }

    /**
     * Show help information
     */
    public function index() {
        $this->show_help();
    }

    /**
     * List failed jobs from database
     * Usage: php index.php cli/DatabaseFailedJobs list [limit] [offset]
     */
    public function list($limit = 20, $offset = 0) {
        $this->console_log("=== Database Failed Jobs ===", 'info');
        
        $failed_jobs = $this->Failed_job_model->get_failed_jobs($limit, $offset);
        $total_count = $this->Failed_job_model->count_failed_jobs();
        
        if (empty($failed_jobs)) {
            $this->console_log("No failed jobs found in database", 'success');
            return;
        }

        $this->console_log("Showing {$limit} of {$total_count} failed jobs (offset: {$offset}):", 'info');
        $this->console_log("");

        foreach ($failed_jobs as $job) {
            $this->console_log("ID: {$job['id']} | UUID: {$job['uuid']}", 'info');
            $this->console_log("  Queue: {$job['queue']}", 'info');
            $this->console_log("  Job Class: {$job['job_class']}", 'info');
            $this->console_log("  Attempts: {$job['attempts']}", 'info');
            $this->console_log("  Failed At: {$job['failed_at']}", 'info');
            $this->console_log("  Error: " . substr($job['error_message'] ?? 'No error message', 0, 100), 'warning');
            $this->console_log("  " . str_repeat("-", 70), 'info');
        }
        
        if ($total_count > ($offset + $limit)) {
            $next_offset = $offset + $limit;
            $this->console_log("", 'info');
            $this->console_log("To see more: php index.php cli/DatabaseFailedJobs list {$limit} {$next_offset}", 'info');
        }
    }

    /**
     * Show detailed information about a specific failed job
     * Usage: php index.php cli/DatabaseFailedJobs show <job_id>
     */
    public function show($job_id) {
        if (empty($job_id)) {
            $this->console_log("Error: Job ID is required", 'error');
            $this->console_log("Usage: php index.php cli/DatabaseFailedJobs show <job_id>", 'info');
            return;
        }

        $job = $this->Failed_job_model->get_failed_job($job_id);
        
        if (!$job) {
            $this->console_log("Failed job with ID '{$job_id}' not found", 'error');
            return;
        }

        $this->console_log("=== Failed Job Details ===", 'info');
        $this->console_log("ID: {$job['id']}", 'info');
        $this->console_log("UUID: {$job['uuid']}", 'info');
        $this->console_log("Queue: {$job['queue']}", 'info');
        $this->console_log("Job Class: {$job['job_class']}", 'info');
        $this->console_log("Connection: {$job['connection']}", 'info');
        $this->console_log("Attempts: {$job['attempts']}", 'info');
        $this->console_log("Failed At: {$job['failed_at']}", 'info');
        $this->console_log("Worker ID: {$job['worker_id']}", 'info');
        $this->console_log("", 'info');
        
        $this->console_log("Error Message:", 'warning');
        $this->console_log($job['error_message'] ?? 'No error message', 'warning');
        $this->console_log("", 'info');
        
        if ($job['error_file']) {
            $this->console_log("Error Location: {$job['error_file']}:{$job['error_line']}", 'warning');
            $this->console_log("", 'info');
        }
        
        $this->console_log("Job Data:", 'info');
        $job_data = json_decode($job['job_data'], true);
        $this->console_log(json_encode($job_data, JSON_PRETTY_PRINT), 'info');
        $this->console_log("", 'info');
        
        if ($job['stack_trace']) {
            $this->console_log("Stack Trace:", 'warning');
            $this->console_log($job['stack_trace'], 'warning');
        }
    }

    /**
     * Get statistics about failed jobs
     * Usage: php index.php cli/DatabaseFailedJobs stats [days]
     */
    public function stats($days = 7) {
        $this->console_log("=== Failed Jobs Statistics (Last {$days} days) ===", 'info');
        
        $stats = $this->Failed_job_model->get_statistics($days);
        
        $this->console_log("Total Failed Jobs: {$stats['total']}", 'warning');
        $this->console_log("", 'info');
        
        if (!empty($stats['by_queue'])) {
            $this->console_log("By Queue:", 'info');
            foreach ($stats['by_queue'] as $queue_stat) {
                $this->console_log("  {$queue_stat['queue']}: {$queue_stat['count']} jobs", 'info');
            }
            $this->console_log("", 'info');
        }
        
        if (!empty($stats['by_job_class'])) {
            $this->console_log("By Job Class:", 'info');
            foreach ($stats['by_job_class'] as $class_stat) {
                $this->console_log("  {$class_stat['job_class']}: {$class_stat['count']} jobs", 'info');
            }
            $this->console_log("", 'info');
        }
        
        if (!empty($stats['by_day'])) {
            $this->console_log("By Day:", 'info');
            foreach ($stats['by_day'] as $day_stat) {
                $this->console_log("  {$day_stat['date']}: {$day_stat['count']} jobs", 'info');
            }
        }
    }

    /**
     * Show common error messages
     * Usage: php index.php cli/DatabaseFailedJobs errors [limit]
     */
    public function errors($limit = 10) {
        $this->console_log("=== Most Common Error Messages ===", 'info');
        
        $errors = $this->Failed_job_model->get_common_errors($limit);
        
        if (empty($errors)) {
            $this->console_log("No error messages found", 'success');
            return;
        }
        
        foreach ($errors as $error) {
            $this->console_log("Count: {$error['count']}", 'warning');
            $this->console_log("Error: " . substr($error['error_message'], 0, 200), 'warning');
            $this->console_log("  " . str_repeat("-", 70), 'info');
        }
    }

    /**
     * Delete a specific failed job
     * Usage: php index.php cli/DatabaseFailedJobs delete <job_id>
     */
    public function delete($job_id) {
        if (empty($job_id)) {
            $this->console_log("Error: Job ID is required", 'error');
            $this->console_log("Usage: php index.php cli/DatabaseFailedJobs delete <job_id>", 'info');
            return;
        }

        $job = $this->Failed_job_model->get_failed_job($job_id);
        if (!$job) {
            $this->console_log("Failed job with ID '{$job_id}' not found", 'error');
            return;
        }

        $result = $this->Failed_job_model->delete_failed_job($job_id);
        
        if ($result) {
            $this->console_log("✓ Failed job '{$job_id}' deleted successfully", 'success');
        } else {
            $this->console_log("✗ Failed to delete job '{$job_id}'", 'error');
        }
    }

    /**
     * Clean up old failed jobs
     * Usage: php index.php cli/DatabaseFailedJobs cleanup [days]
     */
    public function cleanup($days = 30) {
        $this->console_log("=== Cleaning Up Old Failed Jobs ===", 'info');
        $this->console_log("Deleting jobs older than {$days} days...", 'info');
        
        $deleted = $this->Failed_job_model->delete_old_failed_jobs($days);
        
        if ($deleted) {
            $this->console_log("✓ Deleted {$deleted} old failed jobs", 'success');
        } else {
            $this->console_log("No old failed jobs found to delete", 'success');
        }
    }

    /**
     * Clear all failed jobs from database
     * Usage: php index.php cli/DatabaseFailedJobs clear_all
     */
    public function clear_all() {
        $total_count = $this->Failed_job_model->count_failed_jobs();
        
        if ($total_count == 0) {
            $this->console_log("No failed jobs found in database", 'success');
            return;
        }

        $this->console_log("WARNING: This will delete ALL {$total_count} failed jobs from the database!", 'warning');
        $this->console_log("Type 'yes' to confirm: ", 'warning');
        
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($confirmation) === 'yes') {
            $result = $this->Failed_job_model->clear_all_failed_jobs();
            if ($result) {
                $this->console_log("✓ All failed jobs cleared from database", 'success');
            } else {
                $this->console_log("✗ Failed to clear jobs from database", 'error');
            }
        } else {
            $this->console_log("Operation cancelled", 'info');
        }
    }

    /**
     * Export failed jobs to CSV
     * Usage: php index.php cli/DatabaseFailedJobs export [filename]
     */
    public function export($filename = null) {
        if (!$filename) {
            $filename = 'failed_jobs_' . date('Y-m-d_H-i-s') . '.csv';
        }
        
        $this->console_log("=== Exporting Failed Jobs ===", 'info');
        
        $failed_jobs = $this->Failed_job_model->get_export_data();
        
        if (empty($failed_jobs)) {
            $this->console_log("No failed jobs to export", 'success');
            return;
        }
        
        $file_path = FCPATH . $filename;
        $file = fopen($file_path, 'w');
        
        // CSV headers
        fputcsv($file, [
            'UUID', 'Queue', 'Job Class', 'Error Message', 
            'Attempts', 'Failed At', 'Job Data'
        ]);
        
        // CSV data
        foreach ($failed_jobs as $job) {
            fputcsv($file, [
                $job['uuid'],
                $job['queue'],
                $job['job_class'],
                $job['error_message'],
                $job['attempts'],
                $job['failed_at'],
                $job['job_data']
            ]);
        }
        
        fclose($file);
        
        $this->console_log("✓ Exported " . count($failed_jobs) . " failed jobs to: {$file_path}", 'success');
    }

    /**
     * Run database migration to create failed_jobs table
     * Usage: php index.php cli/DatabaseFailedJobs migrate
     */
    public function migrate() {
        $this->console_log("=== Running Failed Jobs Migration ===", 'info');
        
        try {
            $this->load->library('migration');
            
            if ($this->migration->current() === FALSE) {
                $this->console_log("✗ Migration failed: " . $this->migration->error_string(), 'error');
            } else {
                $this->console_log("✓ Migration completed successfully", 'success');
            }
        } catch (Exception $e) {
            $this->console_log("✗ Migration error: " . $e->getMessage(), 'error');
        }
    }

    /**
     * Show help information
     */
    private function show_help() {
        $this->console_log("=== Database Failed Jobs Management CLI ===", 'info');
        $this->console_log("", 'info');
        $this->console_log("Available commands:", 'info');
        $this->console_log("", 'info');
        $this->console_log("  list [limit] [offset]  - List failed jobs from database", 'info');
        $this->console_log("  show <job_id>          - Show detailed job information", 'info');
        $this->console_log("  stats [days]           - Show failed jobs statistics", 'info');
        $this->console_log("  errors [limit]         - Show most common error messages", 'info');
        $this->console_log("  delete <job_id>        - Delete a specific failed job", 'info');
        $this->console_log("  cleanup [days]         - Delete jobs older than X days (default: 30)", 'info');
        $this->console_log("  clear_all              - Clear all failed jobs from database", 'info');
        $this->console_log("  export [filename]      - Export failed jobs to CSV", 'info');
        $this->console_log("  migrate                - Run database migration", 'info');
        $this->console_log("", 'info');
        $this->console_log("Examples:", 'info');
        $this->console_log("  php index.php cli/DatabaseFailedJobs list", 'info');
        $this->console_log("  php index.php cli/DatabaseFailedJobs show 123", 'info');
        $this->console_log("  php index.php cli/DatabaseFailedJobs stats 30", 'info');
        $this->console_log("  php index.php cli/DatabaseFailedJobs cleanup 7", 'info');
        $this->console_log("  php index.php cli/DatabaseFailedJobs export failed_jobs.csv", 'info');
    }

    /**
     * Console logging with colors
     */
    private function console_log($message, $type = 'info') {
        $date = date('Y-m-d H:i:s');
        
        // ANSI color codes
        $colors = [
            'info' => "\033[0m",     // Default
            'success' => "\033[32m",  // Green
            'error' => "\033[31m",    // Red
            'warning' => "\033[33m"   // Yellow
        ];
        
        $color = isset($colors[$type]) ? $colors[$type] : $colors['info'];
        $reset = "\033[0m";
        
        // Don't add timestamp for empty lines
        if (empty(trim($message))) {
            echo PHP_EOL;
        } else {
            echo "{$color}[{$date}] {$message}{$reset}" . PHP_EOL;
        }
    }
}
