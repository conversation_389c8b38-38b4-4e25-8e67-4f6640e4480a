<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Test job for testing error handling in RedisWorker
 * This job intentionally causes different types of errors for testing
 */
class TestErrorJob
{
    protected $CI;

    public function __construct() {
        $this->CI =& get_instance();
    }

    public function handle($data)
    {
        echo "TestErrorJob: Starting job with data: " . json_encode($data) . "\n";
        
        // Check what type of error to simulate
        $error_type = isset($data->error_type) ? $data->error_type : 'none';
        
        switch ($error_type) {
            case 'exception':
                throw new Exception("This is a test exception");
                break;
                
            case 'fatal':
                // This will cause a fatal error
                $this->nonExistentMethod();
                break;
                
            case 'parse':
                // This would cause a parse error if uncommented
                // eval('invalid php syntax here');
                throw new Exception("Parse error simulation (actual parse error would crash PHP)");
                break;
                
            case 'memory':
                // Simulate memory exhaustion
                $memory_hog = [];
                for ($i = 0; $i < 1000000; $i++) {
                    $memory_hog[] = str_repeat('x', 1024);
                }
                break;
                
            case 'timeout':
                // Simulate long-running job
                sleep(120); // 2 minutes
                break;
                
            case 'database':
                // Simulate database error
                $this->CI->load->database();
                $this->CI->db->query("SELECT * FROM non_existent_table");
                break;
                
            case 'file':
                // Simulate file error
                $content = file_get_contents('/non/existent/file.txt');
                break;
                
            case 'warning':
                // Generate a warning
                $array = [];
                echo $array['non_existent_key'];
                echo "Job completed with warning\n";
                break;
                
            case 'success':
                echo "Job completed successfully\n";
                break;
                
            default:
                echo "Job completed with no specific test\n";
                break;
        }
    }
}
